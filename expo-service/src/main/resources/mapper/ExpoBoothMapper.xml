<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoBoothDao">

    <resultMap id="ExpoBoothMap" type="com.echronos.expo.model.ExpoBooth">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="boothName" column="booth_name"/>
        <result property="boothFloor" column="booth_floor"/>
        <result property="boothZone" column="booth_zone"/>
        <result property="boothNumber" column="booth_number"/>
        <result property="boothType" column="booth_type"/>
        <result property="dimensions" column="dimensions"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="shopSkuId" column="shop_sku_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <select id="boothStatistics" resultType="com.echronos.expo.dto.ExpoBoothDTO">
        select
            count(id) AS totalQuantity,
            count(CASE WHEN status = 10 THEN 1 ELSE 0 END) AS sellQuantity,
            count(CASE WHEN status = 20 THEN 1 ELSE 0 END) AS idleQuantity
        from
            ech_expo_booth
        where
            is_deleted = 0 and expo_id = #{expoId}
    </select>

    <select id="pageList" resultType="com.echronos.expo.dto.ExpoBoothDTO">
        select
            eb.*,
            eee.customer_id as exhibitorCustomerId
        from
            ech_expo.ech_expo_booth eb
            left join ech_expo.ech_expo_booth_order_detail ebod on ebod.booth_id = eb.id and ebod.is_deleted = 0
            left join ech_expo.ech_expo_booth_order eebo on eebo.id = ebod.booth_order_id and eebo.is_deleted = 0
            left join ech_expo.ech_expo_exhibitor eee on eee.id = eebo.exhibitor_id and eee.is_deleted = 0
            left join ech_crm_new.ech_customer ecc on ecc.id = eee.customer_id
        where
            eb.is_deleted = 0 and eb.expo_id = #{dto.expoId}
        <if test="null != dto.whereSqlStr and '' != dto.whereSqlStr">
            and ${dto.whereSqlStr}
        </if>
        group by eb.id
        <choose>
            <when test="null != dto.sortStr and '' != dto.sortStr">
                order by ${dto.sortStr}
            </when>
            <otherwise>
                order by eb.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getIndexBoothCount" resultType="com.echronos.expo.model.ext.ExpoIndexCountExt">
        SELECT
            COUNT(b.id) AS total,
            COUNT(CASE WHEN b.status = 20 THEN 1 END) AS calculateTotalOne
        FROM
            ech_expo_booth b
        JOIN
            ech_expo_info i ON b.expo_id = i.id
        WHERE
            i.is_deleted = 0
            AND b.is_deleted = 0
            and b.company_id = #{companyId}
    </select>

</mapper>

