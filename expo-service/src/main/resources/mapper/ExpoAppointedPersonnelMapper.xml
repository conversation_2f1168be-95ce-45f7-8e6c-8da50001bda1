<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAppointedPersonnelDao">

    <resultMap id="ExpoAppointedPersonnelMap" type="com.echronos.expo.model.ExpoAppointedPersonnel">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessType" column="business_type"/>
        <result property="memberId" column="member_id"/>
        <result property="userId" column="user_id"/>
        <result property="companyId" column="company_id"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, business_id, business_type, member_id, user_id, company_id, create_user, update_user, create_time, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=businessId">
                AND business_id = #{businessId}
            </if>
            <if test="null!=businessType">
                AND business_type = #{businessType}
            </if>
            <if test="null!=memberId">
                AND member_id = #{memberId}
            </if>
            <if test="null!=userId">
                AND user_id = #{userId}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>

    <resultMap id="PersonnelTimeMap" type="com.echronos.expo.model.ExpoAppointedPersonnel">
        <id property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessType" column="business_type"/>
        <result property="memberId" column="member_id"/>
        <result property="companyId" column="company_id"/>
        <collection property="timeList" ofType="com.echronos.expo.model.ExpoAppointedPersonnelTime">
            <id property="id" column="time_id"/>
            <result property="personnelId" column="personnel_id"/>
            <result property="startTime" column="start_time"/>
            <result property="endTime" column="end_time"/>
        </collection>
    </resultMap>
    <select id="queryList" resultMap="PersonnelTimeMap">
        select
            eeap.*,
            eeapt.id as time_id,
            eeapt.personnel_id,
            eeapt.start_time,
            eeapt.end_time
        from
            ech_expo_appointed_personnel eeap
                left join ech_expo_appointed_personnel_time eeapt on
                eeap.id = eeapt.personnel_id and eeapt.is_deleted = #{dto.isDeleted}
        where
            eeap.is_deleted = #{dto.isDeleted}
          and eeap.expo_id = #{dto.expoId}
          and eeap.business_id = #{dto.businessId}
          and eeap.business_type = #{dto.businessType}
    </select>

    <select id="queryCanAppointedList" resultType="com.echronos.expo.model.ExpoAppointedPersonnel">
        select
            a.*
        from
            ech_expo_appointed_personnel a
        <choose>
            <when test="1==dto.businessType">
                left join ech_expo_exhibitor b on a.business_id = b.id
            </when>
            <otherwise>
                left join ech_expo_audience b on a.business_id = b.id
            </otherwise>
        </choose>
        where
            a.is_deleted = 0
          and b.is_deleted = 0
          and a.expo_id = #{dto.expoId}
          and b.expo_id = #{dto.expoId}
          and a.business_type = #{dto.businessType}
          and b.is_appoint = #{dto.isAppoint}
          <if test="null!=dto.businessId">
            and a.business_id = #{dto.businessId}
          </if>
    </select>

    <select id="queryInfo" resultType="com.echronos.expo.dto.ExpoAppointedPersonnelDTO">
        select
            a.*,
            b.is_appoint
        from
            ech_expo_appointed_personnel a
        <choose>
            <when test="1==dto.businessType">
                left join ech_expo_exhibitor b on a.business_id = b.id
            </when>
            <otherwise>
                left join ech_expo_audience b on a.business_id = b.id
            </otherwise>
        </choose>
        where
            a.is_deleted = 0
            and b.is_deleted = 0
            and a.id = #{dto.id}
            and a.expo_id = #{dto.expoId}
            and b.expo_id = #{dto.expoId}
            and a.business_type = #{dto.businessType}
    </select>
</mapper>

