<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoExhibitorScanRecordDao">

    <resultMap id="ExpoExhibitorScanRecordMap" type="com.echronos.expo.model.ExpoExhibitorScanRecord">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="exhibitorId" column="exhibitor_id"/>
        <result property="audienceId" column="audience_id"/>
        <result property="companyId" column="company_id"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, exhibitor_id, audience_id, company_id, create_user, create_time, update_user, update_time, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=exhibitorId">
                AND exhibitor_id = #{exhibitorId}
            </if>
            <if test="null!=audienceId">
                AND audience_id = #{audienceId}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>

    <resultMap id="ExpoExhibitorScanAudienceMap" type="com.echronos.expo.dto.ExpoExhibitorScanRecordDTO">
        <result property="exhibitorId" column="exhibitor_id"/>
        <result property="exhibitorName" column="exhibitor_name"/>
        <collection property="audienceInfo" ofType="com.echronos.expo.dto.ExpoExhibitorScanAudienceInfoDTO">
            <result property="id" column="audience_id"/>
            <result property="name" column="audience_name"/>
            <result property="createTime" column="create_time"/>
        </collection>
    </resultMap>

    <select id="countScanRecordsByDate" resultType="com.echronos.expo.model.ext.ExpoExhibitorScanCountExt">
        SELECT
            DATE(create_time) as scanDate,
            COUNT(*) as scanCount
        FROM ech_expo_exhibitor_scan_record
        WHERE expo_id = #{expoId}
          AND is_deleted = 0
        GROUP BY DATE(create_time)
        ORDER BY scanDate
    </select>

    <select id="countScanRecordsByHour" resultType="com.echronos.expo.model.ext.ExpoExhibitorScanCountExt">
        SELECT
            HOUR(create_time) as scanTime,
            COUNT(*) as scanCount
        FROM ech_expo_exhibitor_scan_record
        WHERE expo_id = #{expoId}
          AND DATE(create_time) = #{statisticsDate}
          AND is_deleted = 0
        GROUP BY HOUR(create_time)
        ORDER BY scanCount
    </select>

    <select id="countScanRecordsByExhibitor" resultType="com.echronos.expo.model.ext.ExpoExhibitorScanCountExt">
        SELECT
            exhibitor_id as exhibitorId,
            COUNT(*) as scanCount
        FROM ech_expo_exhibitor_scan_record
        WHERE expo_id = #{expoId}
          AND is_deleted = 0
        GROUP BY exhibitorId
        ORDER BY scanCount DESC
    </select>

    <select id="pageExhibitorScanRecordList" resultType="com.echronos.expo.model.ext.ExpoExhibitorScanRecordExt">
        SELECT
            esr.exhibitor_id as exhibitorId,
            ecc.company_name as exhibitorName,
            COUNT(DISTINCT esr.audience_id) as audienceCount,
            COUNT(*) as scanCount
        FROM ech_expo_exhibitor_scan_record esr
        LEFT JOIN ech_expo_exhibitor ee ON esr.exhibitor_id = ee.id
        LEFT JOIN ech_expo_audience ea ON esr.audience_id = ea.id
        INNER JOIN ech_crm_new.ech_customer ecc ON ecc.customer_company_id = ee.customer_company_id
        WHERE esr.is_deleted = 0
          AND esr.expo_id = #{dto.expoId}
        GROUP BY esr.exhibitor_id
    </select>

    <select id="pageExhibitorScanAudienceList" resultMap="ExpoExhibitorScanAudienceMap">
        SELECT
            ee.id as exhibitor_id,
            ee.customer_company_id as exhibitor_company_id,
            ecc.company_name as exhibitor_name,
            ea.id as audience_id,
            ea.name as audience_name,
            esr.create_time as create_time
        FROM ech_expo_exhibitor_scan_record esr
        LEFT JOIN ech_expo_exhibitor ee ON esr.exhibitor_id = ee.id AND ee.is_deleted = 0
        LEFT JOIN ech_expo_audience ea ON esr.audience_id = ea.id AND ea.is_deleted = 0
        INNER JOIN ech_crm_new.ech_customer ecc ON ecc.customer_company_id = ee.customer_company_id
        WHERE esr.is_deleted = 0
          AND esr.expo_id = #{dto.expoId}
          AND esr.exhibitor_id = #{dto.exhibitorId}
    </select>

</mapper>

