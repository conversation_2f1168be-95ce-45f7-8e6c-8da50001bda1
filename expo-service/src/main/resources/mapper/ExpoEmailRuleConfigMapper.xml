<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoEmailRuleConfigDao">

    <resultMap id="ExpoEmailRuleConfigMap" type="com.echronos.expo.model.ExpoEmailRuleConfig">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="eventId" column="event_id"/>
        <result property="templateId" column="template_id"/>
        <result property="ruleType" column="rule_type"/>
        <result property="triggerDay" column="trigger_day"/>
        <result property="sendTime" column="send_time"/>
        <result property="smtpConfigId" column="smtp_config_id"/>
        <result property="status" column="status"/>
        <result property="companyId" column="company_id"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="audienceRole" column="audience_role"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, rule_name, event_id, template_id, rule_type, trigger_day, send_time, smtp_config_id, audience_role, status, company_id, is_deleted, create_user, update_user, create_time, update_time
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=ruleName and ''!=ruleName">
                AND rule_name = #{ruleName}
            </if>
            <if test="null!=eventId">
                AND event_id = #{eventId}
            </if>
            <if test="null!=templateId">
                AND template_id = #{templateId}
            </if>
            <if test="null!=ruleType">
                AND rule_type = #{ruleType}
            </if>
            <if test="null!=triggerDay">
                AND trigger_day = #{triggerDay}
            </if>
            <if test="null!=sendTime">
                AND send_time = #{sendTime}
            </if>
            <if test="null!=smtpConfigId">
                AND smtp_config_id = #{smtpConfigId}
            </if>
            <if test="null!=audienceRole">
                AND audience_role = #{audienceRole}
            </if>
            <if test="null!=status">
                AND status = #{status}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
        </where>
    </sql>
</mapper>

