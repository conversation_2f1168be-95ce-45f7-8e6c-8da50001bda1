server:
  port: 8189
  servlet:
    context-path: /
  max-http-header-size: 64KB
spring:
  application:
    name: ech-expo-hql
  cloud:
    nacos:
      discovery:
        server-addr: ${spring_cloud_nacos_discovery_server-addr:nacos-test.myhuahua.com:8848}
        register-enabled: true
        ip: ***********
  #spring datasource配置
  datasource:
    name: hsj #数据源编码
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${spring_datasource_username:root}
    password: ${spring_datasource_password:Style1394.com@@}
    url: ${spring_datasource_url:*****************************************************************************************************************************************************************************}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 200
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      connection-init-sql: set names utf8mb4
  jackson:
    default-property-inclusion: non_null
    #    date-format: yyyy-MM-dd HH:mm:ss.Z
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: true
  aop:
    auto: true
  main:
    allow-bean-definition-overriding: true
  redis:
    database: ${spring_redis_database:0}
    host: ${spring_redis_host:redis-test.myhuahua.com}
    port: ${spring_redis_port:6379}
    password: ${spring_redis_password:}
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  rabbitmq:
    addresses: ${spring_rabbitmq_addresses:rabbitmq-test.myhuahua.com:5672}
    username: ${spring_rabbitmq_username:admin}
    password: ${spring_rabbitmq_password:Style1394.com@@}
    virtual-host: dev
    # 开启ACK
    listener:
      simple:
        acknowledge-mode: manual
        prefetch: 1
    publisher-returns: true
    publisher-confirms: true
    connection-timeout: 10000
  thymeleaf:
    cache: false
    check-template-location: true
    enabled: true
    suffix: .html
    prefix: classpath:/templates/
    encoding: utf-8
    mode: HTML5
    resources:
      chain:
        strategy:
          content:
            enabled: true
            paths: /**
mybatis-plus:
  #mapper.xml存放路径
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    #使用日志类
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl
  #表映射对象路径
  type-aliases-package: com.echronos.expo.model
#management:
#  endpoints:
#    web:
#      exposure:
#        include: health
feign:
  client:
    config:
      default:
        connectTimeout: 60000 # 连接超时时间
        readTimeout: 60000   # 读超时时间
        loggerLevel: full # 日志级别
  httpclient:
    enabled: true
  okhttp:
    enabled: false
swagger.enable: ${swagger_enable:true}
#需要记录JOB运行情况设为true并配置数据库相关信息
event.trace.rdb.enable: false
##ZK集群地址
elastic.job.serverList: ${elastic_job_serverList:zookeeper-test.myhuahua.com:2181}
##存储空间
elastic.job.namespace: job-expo
elastic.job.maxSleepTimeMilliseconds: 3000

remote:
  gateway:
    domain: ${remote_gateway_domain:https://gate-test.myhuahua.com}
  h5:
    domain: ${remote_h5_domain:https://h5-test.myhuahua.com}