package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.param.ExpoPageParam;
import com.echronos.expo.service.ExpoStatisticsService;
import com.echronos.expo.vo.statistics.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展会总报表
 * <AUTHOR>
 * @date 2025-08-15 14:55
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/index/statistics")
public class ExpoIndexStatisticsController {

    @Resource
    private ExpoStatisticsService expoStatisticsService;

    /**
     * 展会总数（首页）
     * @return
     */
    @GetMapping(value = "/expo/count")
    public Result<ExpoIndexCountVO> getIndexExpoInfoCount(){
        return Result.build(expoStatisticsService.getIndexExpoCount(RequestUserUtils.getUser().getCompanyId()));
    }

    /**
     * 参展商总数（首页）
     * @return
     */
    @GetMapping(value = "/exhibitor/count")
    public Result<ExpoIndexCountVO> getIndexExhibitorCount(){
        return Result.build(expoStatisticsService.getIndexExhibitorCount(RequestUserUtils.getUser().getCompanyId()));
    }

    /**
     * 观众总数（首页）
     * @return
     */
    @GetMapping(value = "/audience/count")
    public Result<ExpoIndexCountVO> getIndexAudienceCount(){
        return Result.build(expoStatisticsService.getIndexAudienceCount(RequestUserUtils.getUser().getCompanyId()));
    }


    /**
     * 展位预订率（首页）
     * @return
     */
    @GetMapping(value = "/booth/count")
    public Result<ExpoIndexCountVO> getIndexBoothCount(){
        return Result.build(expoStatisticsService.getIndexBoothCount(RequestUserUtils.getUser().getCompanyId()));
    }

    /**
     * 展商分析报表（首页）
     * @return
     */
    @GetMapping(value = "/exhibitor/analyze/count")
    public Result<ExpoIndexExhibitorAnalyzeVO> getIndexExhibitorAnalyzeCount(){
        return Result.build(expoStatisticsService.getIndexExhibitorAnalyzeCount(RequestUserUtils.getUser().getCompanyId()));
    }

    /**
     * 展商分析报表-展商首次参展和复参展（首页-明细）
     * @return
     */
    @GetMapping(value = "/exhibitor/analyze/firstAndRepeat/count")
    public Result<ExpoIndexExhibitorAnalyzeFirstAndRepeatVO> getIndexExhibitorAnalyzeFirstAndRepeatCount(){
        return Result.build(expoStatisticsService.getIndexExhibitorAnalyzeFirstAndRepeatCount(RequestUserUtils.getUser().getCompanyId()));
    }

    /**
     * 展商分析报表-展商参展频率（首页-明细）
     * @return
     */
    @GetMapping(value = "/exhibitor/analyze/frequency/count")
    public Result<ExpoIndexExhibitorAnalyzeFrequencyVO> getIndexExhibitorAnalyzeFrequencyCount(){
        return Result.build(expoStatisticsService.getIndexExhibitorAnalyzeFrequencyCount(RequestUserUtils.getUser().getCompanyId()));
    }


    /**
     * 展商分析报表-展商详细列表（首页-明细）
     * @return
     */
    @GetMapping(value = "/exhibitor/analyze/detail/list")
    public Result<List<ExpoIndexExhibitorAnalyzeDetailVO>> getIndexExhibitorAnalyzeDetailList(ExpoPageParam param){
        ExpoExhibitorDTO dto = new ExpoExhibitorDTO();
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setPageNo(param.getPageNo());
        dto.setPageSize(param.getPageSize());
        return expoStatisticsService.getIndexExhibitorAnalyzeDetailList(dto);
    }



}
