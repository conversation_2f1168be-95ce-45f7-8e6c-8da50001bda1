package com.echronos.expo.controller;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.echronos.commons.Result;
import com.echronos.commons.annotation.Permission;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.annotation.PreventDuplication;
import com.echronos.expo.constants.GatewayRoutingConstants;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.model.ExpoForm;
import com.echronos.expo.param.*;
import com.echronos.expo.param.appoint.*;
import com.echronos.expo.param.audience.ExpoChannelRegisterQrParam;
import com.echronos.expo.service.IExpoAppointService;
import com.echronos.expo.service.IExpoAudienceService;
import com.echronos.expo.service.impl.ExpoFormService;
import com.echronos.expo.util.IpUtil;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.appoint.AppointDetailVO;
import com.echronos.expo.vo.appoint.AppointTimeListVO;
import com.echronos.expo.vo.appoint.AppointmentSetVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 观众管理
 *
 * <AUTHOR>
 * @Date 20025/5/16 9:44
 * @ClassName ExpoAudienceController
 */
@RestController
@RequestMapping("v1/expo/audience")
public class ExpoAudienceController {
    @Resource
    private IExpoAudienceService expoAudienceService;
    @Resource
    private IExpoAppointService expoAppointService;
    @Resource
    private ExpoFormService expoFormService;
    @Value("${remote.h5.domain}")
    private String gateway;

    /**
     * 观众分页查询
     *
     * @param param
     * @return
     */
    @PostMapping("/pageFor")
    @Permission({"BV_001_002_003_001"})
    public Result<List<ExpoAudienceVO>> pageFor(@RequestBody ExpoAudienceListFilterParam param) {
        Result<List<ExpoAudienceVO>> listResult = expoAudienceService.pageFor(param);
        return listResult;
    }

    /**
     * 分页查询观众列表
     * @param param
     * @return
     */
    @PostMapping("/getPageList")
    public Result<List<ExpoAudienceVO>> getPageList(@RequestBody ExpoAudienceListFilterParam param){
        ExpoAudiencePageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudiencePageDTO.class);
        Result<List<ExpoAudienceVO>> result = expoAudienceService.pageList(dto);
        return result;
    }

    /**
     * 获取观众签到打印签到页面列表
     *
     * @param param
     * @return
     */
    @PostMapping("/printSignPageList")
    public Result<List<AudiencePrintSignVO>> printSignPageList(@RequestBody ExpoAudiencePrintSignParam param) {
        ExpoAudiencePageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudiencePageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoAudienceService.printSignPageList(dto);
    }

    /**
     * 添加打印次数
     * @param param
     * @return
     */
    @PreventDuplication(module = "add_print_frequency", expire = 2, msg = "正在执行请勿重复点击！")
    @PostMapping(value = "/addPrintFrequency")
    public Result addPrintFrequency(@RequestBody IdParam param){
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAudienceService.addPrintFrequency(dto);
        return Result.build();
    }

    /**
     * 观众详情
     *
     * @param param
     * @return
     */
    @GetMapping("/info")
    @Permission({"BV_001_002_003_001_001"})
    public Result<ExpoAudienceVO> info(ExpoAudienceParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoAudienceService.info(dto);
    }

    /**
     * 观众列表
     *
     * @param param
     * @return
     */
    @GetMapping("/list")
    public Result<List<ExpoAudienceVO>> list(ExpoAudienceListFilterParam param) {
        ExpoAudienceListDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceListDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        Result<List<ExpoAudienceVO>> listResult = expoAudienceService.list(dto);
        return listResult;
    }

    /**
     * 观众ID列表
     *
     * @param param
     * @return
     */
    @PostMapping("/idList")
    public Result<List<Integer>> idList(@RequestBody ExpoAudienceListFilterExportParam param) {
        List<ExpoAudienceDTO> list = expoAudienceService.list(param);
        List<Integer> idList = list.stream().map(ExpoAudienceDTO::getId).collect(Collectors.toList());
        return Result.build(idList);
    }

    /**
     * 观众导出
     *
     * @param response
     */
    @PostMapping("exportExcel")
    @Permission({"BV_001_002_003_003"})
    public void exportExcel(HttpServletResponse response, @RequestBody ExpoAudienceListFilterExportParam param) {
        expoAudienceService.exportExcel(response, param, RequestUserUtils.getUser());
    }

    /**
     * 观众导入模板下载
     *
     * @param response
     */
    @GetMapping("downLoadTemplate")
    @Permission({"BV_001_002_003_004"})
    public void downLoadTemplate(IdParam param, HttpServletResponse response) {
        expoAudienceService.exportData(param.getId(), response, RequestUserUtils.getUser(), null);
//        expoAudienceService.downLoadTemplateExcel(response, RequestUserUtils.getUser());
    }

    /**
     * 观众导入数据检查
     *
     * @param param
     * @return
     */
    @PostMapping("checkImportExcel")
    @Permission({"BV_001_002_003_004"})
    public Result<AudienceImportResultVO> checkImportExcel(ExpoAudienceImportParam param) {
//        AudienceImportResultVO vo = expoAudienceService.checkImportExcel(param, RequestUserUtils.getUser());
        AudienceImportResultVO vo = expoAudienceService.checkImportExcelV2(param, RequestUserUtils.getUser());
        return Result.build(vo);
    }

    /**
     * 观众导入
     *
     * @param param
     * @return
     */
    @PostMapping("importExcel")
    @Permission({"BV_001_002_003_004"})
    public Result<AudienceImportResultVO> importExcel(ExpoAudienceImportParam param) {
        AudienceImportResultVO vo = expoAudienceService.importExel(param, RequestUserUtils.getUser());
        return Result.build(vo);
    }

    /**
     * 观众编辑修改
     *
     * @param param
     * @return
     */
    @PostMapping("edit")
    @Permission({"BV_001_002_003_001_002"})
    public Result<ExpoAudienceScanVO> edit(@RequestBody ExpoAudienceEditParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoAudienceService.edit(dto);
    }

    /**
     * 删除观众
     *
     * @param param
     * @return
     */
    @PostMapping("delete")
    @Permission({"BV_001_002_003_001_004"})
    public Result delete(@RequestBody ExpoAudienceDelParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoAudienceService.del(dto);
    }

    /**
     * 后台签到
     *
     * @param param
     * @return
     */
    @PostMapping("admin/sign")
    @Permission({"BV_001_002_003_001_003"})
    public Result<ExpoAudienceVO> adminSign(@RequestBody ExpoAudienceSignParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoAudienceService.sign(dto);
    }

    /**
     * 发送邮件
     *
     * @param param
     * @return
     */
    @PostMapping("sendEmail")
    @Permission({"BV_001_002_003_002"})
    public Result sendEmail(@RequestBody ExpoAudienceSendEmailParam param) {
        ExpoAudienceEmailRecordsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceEmailRecordsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAudienceService.sendEmail(dto);
        return Result.build();
    }

    /**
     * 观众扫码H5加载表单信息
     *
     * @param param
     * @return
     */
    @GetMapping("official/register/qrCode")
    public Result<ExpoFormVO> scanForm(ExpoFormParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        return expoAudienceService.scanRegisterForm(dto);
    }


    /**
     * H5观众提交注册
     *
     * @param param
     * @return
     */
    @PostMapping("official/register")
    public Result<ExpoAudienceScanVO> register(@RequestBody ExpoAudienceAddParam param, HttpServletRequest request) {
        String ipAddress = IpUtil.api.getIpAddress(request);
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setIp(ipAddress);
        return expoAudienceService.register(dto);
    }

    /**
     * H5观众签到
     *
     * @param param
     * @return
     */
    @PostMapping("official/sign")
    public Result<ExpoAudienceVO> sign(@RequestBody ExpoAudienceSignParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        return expoAudienceService.sign(dto);
    }

    /**
     * 获取观众二维码信息
     *
     * @param param
     * @return
     */
    @GetMapping(value = "qrcode/info")
    public Result<ExpoAudienceQrCodeInfoVO> getQrcodeInfo(ExpoAudienceQrcodeParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        return Result.build(expoAudienceService.getQrCodeInfo(dto));
    }

    /**
     * 列表筛选使用获取字典值列表
     *
     * @return
     */
    @GetMapping("dictByColName")
    public Result<List<ExpoCodeValueVO>> dictByColName(ExpoAudienceDictParam param) {
        Integer companyId = RequestUserUtils.getUser().getCompanyId();
        return expoAudienceService.dictByColName(param.getColName(), companyId, param.getExpoId());
    }

    /**
     * 观众注册及编辑字典列表
     *
     * @return
     */
    @GetMapping("dictList")
    public Result<ExpoDictCodeValueVO> dictList() {
        return expoAudienceService.dictList();
    }


    /**
     * 预约设置
     *
     * @param param 参数
     */
    @PutMapping("appoint/set")
    public Result<?> appointSet(@RequestBody AppointSetParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setMemberId(RequestUserUtils.getUser().getMemberId());
        dto.setCreateUser(RequestUserUtils.getUser().getId());
        expoAudienceService.appointSet(dto);
        return Result.build();
    }

    /**
     * 查询预约设置
     *
     * @param param 参数
     * @return AppointmentSetVO
     */
    @GetMapping("appoint/get/set")
    public Result<AppointmentSetVO> getAppointSet(AppointSetGetParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AppointmentSetVO appointmentSetVO = expoAppointService.getAppointSet(dto);
        return Result.build(appointmentSetVO);
    }

    /**
     * 设置时间
     *
     * @param param 参数
     */
    @PutMapping("appoint/set/time")
    public Result<?> appointSetTime(@RequestBody AppointSetTimeParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        expoAppointService.appointSetTime(dto);
        return Result.build();
    }

    /**
     * 预约展商
     *
     * @param param 参数
     */
    @PostMapping("appoint/exhibitor")
    public Result<?> appointExhibitor(@RequestBody AppointSubmitParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCreateUser(RequestUserUtils.getUser().getId());
        dto.setMemberId(RequestUserUtils.getUser().getMemberId());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointSubmit(dto);
        return Result.build();
    }

    /**
     * 展会预约时间列表
     *
     * @param param 参数
     * @return List<AppointTimeListVO>
     */
    @GetMapping("appoint/time/list")
    public Result<List<AppointTimeListVO>> timeList(AppointSetGetParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<AppointTimeListVO> list = expoAppointService.timeList(dto);
        return Result.build(list);
    }

    /**
     * 预约详情
     *
     * @param param 参数
     * @return AppointDetailVO
     */
    @GetMapping("appoint/detail")
    public Result<AppointDetailVO> appointDetail(AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AppointDetailVO appointDetailVO = expoAppointService.appointDetail(dto);
        return Result.build(appointDetailVO);
    }

    /**
     * 接受预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/accept")
    public Result<?> appointAccept(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.ACCEPTED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }

    /**
     * 拒绝预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/refuse")
    public Result<?> appointRefuse(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.REJECTED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }

    /**
     * 取消预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/cancel")
    public Result<?> appointCancel(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.CANCELLED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }


    /**
     * 获取观众注册表单
     * @param param 表单参数
     * @return
     */
    @GetMapping(value = "register/form")
    public Result<FormCodeVO> getRegisterForm(IdParam param){
        ExpoForm expoForm = expoFormService.getFormByType(param.getId(),
                ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        FormCodeVO expoFormVO = CopyObjectUtils.copyAtoB(expoForm, FormCodeVO.class);
        return Result.build(expoFormVO);
    }

    /**
     * 获取渠道注册二维码
     * @param param
     * @return
     */
    @GetMapping("channel/register/qr")
    public Result<ExpoQrCodeInfoVO> getChannelRegisterQr(ExpoChannelRegisterQrParam param){
        RequestUser user = RequestUserUtils.getUser();
        ExpoAudienceDTO dto = new ExpoAudienceDTO();
        dto.setChannelId(param.getChannelId());
        dto.setExpoId(param.getExpoId());
        dto.setCompanyId(user.getCompanyId());
        dto.setMemberId(user.getMemberId());
        ExpoQrCodeInfoVO vo = expoAudienceService.getChannelRegisterQr(dto);
        return Result.build(vo);
    }

    /**
     * 获取跳转扫码地址
     * @return
     */
    @GetMapping(value = "location/can/qr/url")
    public Result<ExpoQrCodeInfoVO> getLocationCanQrUrl(){
        String url = gateway + GatewayRoutingConstants.locationCanQrUrl;
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(url, qrConfig, "png");
        ExpoQrCodeInfoVO vo = new ExpoQrCodeInfoVO();
        vo.setQrcodeUrl(url);
        vo.setQrcodeImage(png);
        return Result.build(vo);
    }

    /**
     * 观众提交注册信息
     *
     * @param param
     * @return
     */
    @PostMapping("register/submit")
    public Result<ExpoAudienceScanVO> registerSubmit(@RequestBody ExpoAudienceAddParam param, HttpServletRequest request) {
        String ipAddress = IpUtil.api.getIpAddress(request);
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        dto.setIp(ipAddress);
        return Result.build(expoAudienceService.registerSubmit(dto));
    }

    /**
     * 校验是否已注册
     * @param param
     * @return
     */
    @GetMapping("register/check")
    public Result<AudienceRegisterVO> checkRegister(ExpoAudienceCheckRegisterParam param) {
        ExpoAudienceDTO dto = new ExpoAudienceDTO();
        dto.setExpoId(param.getExpoId());
        dto.setEmail(param.getEmail());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AudienceRegisterVO vo = new AudienceRegisterVO();
        vo.setRegister(expoAudienceService.checkRegister(dto));
        return Result.build(vo);
    }

}
