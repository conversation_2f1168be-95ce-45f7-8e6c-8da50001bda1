package com.echronos.expo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.commons.Result;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.param.ExpoPageParam;
import com.echronos.expo.param.ExpoStatisticsParam;
import com.echronos.expo.service.IExpoAudienceService;
import com.echronos.expo.service.IExpoBoothService;
import com.echronos.expo.service.IExpoExhibitorService;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.exhibitor.ExhibitorNumberVO;
import com.echronos.expo.vo.exhibitor.ExpoExhibitorOtherInfoStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 展会数据统计
 *
 * <AUTHOR>
 * date2025/8/11 11:19
 */
@RestController
@RequestMapping(value = "v1/expo/exhibitor/statistics")
public class ExpoExhibitorStatisticsController {

    @Autowired
    private IExpoExhibitorService expoExhibitorService;
    @Autowired
    private IExpoBoothService expoBoothService;
    @Autowired
    private IExpoAudienceService expoAudienceService;

    /**
     * 展商数量统计
     *
     * @return
     */
    @GetMapping(value = "/total")
    public Result<ExhibitorNumberVO> expoExhibitorNumber(ExpoStatisticsParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        return Result.build(expoExhibitorService.queryExhibitorTotal(dto));
    }

    /**
     * 展位销售率
     *
     * @return
     */
    @GetMapping(value = "/booth/sale")
    public Result<ExpoBoothConversionRateVO> expoBoothSale(ExpoStatisticsParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        return Result.build(expoBoothService.expoBoothSale(dto));
    }


    /**
     * 展位类型分布
     *
     * @param param
     * @return
     */
    @GetMapping(value = "/booth/type")
    public Result<List<ExpoBoothTypeVO>> expoBootType(ExpoStatisticsParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        return Result.build(expoBoothService.expoBootType(dto));
    }

    /**
     * 企业信息收集状态
     *
     * @return
     */
    @GetMapping(value = "/enterprise/info/status")
    public Result<ExpoExhibitorOtherInfoStatusVO> expoExhibitorEnterpriseInfoStatus(ExpoStatisticsParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        return Result.build(expoExhibitorService.expoExhibitorEnterpriseInfoStatus(dto));
    }

    /**
     * 会刊信息收集状态
     *
     * @param param
     * @return
     */
    @GetMapping(value = "/journal/info/status")
    public Result<ExpoExhibitorOtherInfoStatusVO> expoExhibitorJournalInfoStatus(ExpoStatisticsParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        return Result.build(expoExhibitorService.expoExhibitorJournalInfoStatus(dto));
    }

    /**
     * 展位预定率
     *
     * @return
     */
    @GetMapping(value = "/booth/reserve/rate")
    public Result<ExpoBoothReserveRateVO> expoBoothReserveRate(ExpoStatisticsParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        return Result.build(expoBoothService.expoBoothReserveRate(dto));
    }

    /**
     * 观众分析报表统计
     *
     * @return
     */
    @GetMapping(value = "/audience/report/form")
    public Result<ExpoAudienceReportFormVO> expoAudienceReportForm(ExpoStatisticsParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        return Result.build(expoAudienceService.expoAudienceReportForm(dto));
    }

    /**
     * 观众分析报表统计详情
     *
     * @param param
     * @return
     */
    @GetMapping(value = "/audience/report/form/detail")
    public Result<ExpoAudienceReportFormVO> expoAudienceReportFormDetail(ExpoStatisticsParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        return Result.build(expoAudienceService.expoAudienceReportFormDetail(dto));
    }

    /**
     * 观众详情列表
     *
     * @param
     * @return
     */
    @GetMapping(value = "/audience/detail")
    public Result<List<ExpoAudienceDetailVO>> expoAudienceDetail(ExpoPageParam param) {
        ExpoAudienceDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAudienceDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        IPage<ExpoAudienceDetailVO> page = expoAudienceService.expoAudienceDetail(dto);
        return Result.build(page.getRecords(), page.getTotal());
    }

}
