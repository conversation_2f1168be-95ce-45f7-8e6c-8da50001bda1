package com.echronos.expo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoEmailRuleConfigDTO;
import com.echronos.expo.enums.ExpoAudienceRoleEnum;
import com.echronos.expo.enums.ExpoEmailSendEventEnum;
import com.echronos.expo.param.*;
import com.echronos.expo.service.IExpoEmailRuleConfigService;
import com.echronos.expo.vo.ExpoCodeValueVO;
import com.echronos.expo.vo.ExpoEmailRuleConfigVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 邮件自动发送规则相关接口
 *
 * <AUTHOR>
 * @date 2025/8/26 17:21
 */
@RestController
@RequestMapping("/v1/expo/email/rule")
public class ExpoEmailRuleConfigController extends BaseController {

    @Resource
    private IExpoEmailRuleConfigService emailRuleConfigService;

    /**
     * 获取邮件自动发送事件列表
     *
     * @return List<ExpoCodeValueVO>
     */
    @GetMapping("/event/list")
    public Result<List<ExpoCodeValueVO>> eventList() {
        return Result.build(ExpoEmailSendEventEnum.getVO());
    }

    /**
     * 获取邮件自动发送收件观众角色列表
     *
     * @return List<ExpoCodeValueVO>
     */
    @GetMapping("/audience/role/list")
    public Result<List<ExpoCodeValueVO>> audienceRoleList() {
        return Result.build(ExpoAudienceRoleEnum.getVO());
    }

    /**
     * 新增邮件自动发送规则
     *
     * @param param 参数
     */
    @PostMapping("/add")
    public Result<?> add(@RequestBody ExpoEmailRuleAddParam param) {
        // 校验权限
//        checkPer(param.getRuleType(), "a", "b");
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        emailRuleConfigService.add(dto);
        return Result.build();
    }

    /**
     * 删除邮件自动发送规则
     *
     * @param param 参数
     */
    @PutMapping("/del")
    public Result<?> del(@RequestBody ExpoEmailRuleDetailParam param) {
        // 校验权限
//        checkPer(param.getRuleType(), "a", "b");
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        dto.setUpdateUser(getUserId());
        emailRuleConfigService.del(dto);
        return Result.build();
    }

    /**
     * 修改邮件自动发送规则
     *
     * @param param 参数
     */
    @PostMapping("/update")
    public Result<?> update(@RequestBody ExpoEmailRuleUpdateParam param) {
        // 校验权限
//        checkPer(param.getRuleType(), "a", "b");
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        emailRuleConfigService.update(dto);
        return Result.build();
    }

    /**
     * 获取邮件自动发送规则详情
     *
     * @param param 参数
     * @return ExpoEmailRuleConfigVO
     */
    @GetMapping("/detail")
    public Result<ExpoEmailRuleConfigVO> detail(ExpoEmailRuleDetailParam param) {
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        return Result.build(emailRuleConfigService.detail(dto));
    }

    /**
     * 获取邮件自动发送规则配置分页列表
     *
     * @param param 参数
     * @return List<ExpoEmailRuleConfigVO>
     */
    @GetMapping("/page/list")
    public Result<List<ExpoEmailRuleConfigVO>> pageList(ExpoEmailRuleListParam param) {
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        Page<ExpoEmailRuleConfigVO> page = emailRuleConfigService.pageList(dto);
        return Result.build(page.getRecords(), page.getTotal());
    }

    /**
     * 修改邮件自动发送规则状态（启用，禁用）
     *
     * @param param 参数
     */
    @PostMapping("/update/status")
    public Result<?> updateStatus(@RequestBody ExpoEmailRuleUpdateStatusParam param) {
        // 校验权限
//        checkPer(param.getRuleType(), "a", "b");
        ExpoEmailRuleConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoEmailRuleConfigDTO.class);
        dto.setCompanyId(getCompanyId());
        emailRuleConfigService.updateStatus(dto);
        return Result.build();
    }

    /**
     * 校验权限
     *
     * @param ruleType
     * @param audiencePer
     * @param exhibitorPer
     */
    private void checkPer(Integer ruleType, String audiencePer, String exhibitorPer) {
        if (ruleType == 0) {
            checkPermission(audiencePer);
        }
        if (ruleType == 1) {
            checkPermission(exhibitorPer);
        }
    }
}
