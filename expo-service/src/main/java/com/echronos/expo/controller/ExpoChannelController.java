package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.annotation.Permission;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoChannelDTO;
import com.echronos.expo.dto.ExpoChannelPageDTO;
import com.echronos.expo.param.*;
import com.echronos.expo.service.IExpoChannelService;
import com.echronos.expo.vo.ExpoChannelVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @Date 20025/5/15 14:23
 * @ClassName ExpoChannelController
 */
@RestController
@RequestMapping("v1/expo/channel")
public class ExpoChannelController {

    @Resource
    private IExpoChannelService expoChannelService;

    /**
     * 分页查询渠道
     *
     * @param param
     * @return
     */
    @GetMapping("pageFor")
    @Permission({"BV_001_003_001_001"})
    public Result<List<ExpoChannelVO>> pageFor(ExpoChannelPageParam param) {
        ExpoChannelPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoChannelService.pageFor(dto);
    }

    /**
     * 渠道列表
     *
     * @param param
     * @return
     */
    @GetMapping("channelList")
    public Result<List<ExpoChannelVO>> channelList(ExpoChannelParam param) {
        ExpoChannelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoChannelService.channelList(dto);
    }

    /**
     * 根据公司ID查询所有渠道列表
     *
     * @param param
     * @return
     */
    @GetMapping("allChannelList")
    public Result<List<ExpoChannelVO>> getAllChannel(ExpoChannelCompanyParam param) {
        ExpoChannelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelDTO.class);
        dto.setCompanyId(param.getCompanyId());
        dto.setId(param.getChannelId());
        return expoChannelService.channelList(dto);
    }


    /**
     * 新增渠道
     *
     * @param param
     * @return
     */
    @PostMapping("add")
    @Permission({"BV_001_003_001_002"})
    public Result add(@RequestBody ExpoChannelAddParam param) {
        ExpoChannelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoChannelService.save(dto);
    }

    /**
     * 渠道编辑
     *
     * @param param
     * @return
     */
    @PostMapping("edit")
    @Permission({"BV_001_003_001_003"})
    public Result edit(@RequestBody ExpoChannelEditParam param) {
        ExpoChannelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoChannelService.save(dto);
    }

    /**
     * 渠道删除
     *
     * @param param
     * @return
     */
    @PostMapping("delete")
    @Permission({"BV_001_003_001_004"})
    public Result delete(@RequestBody ExpoChannelEditParam param) {
        ExpoChannelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return expoChannelService.del(dto);
    }
}
