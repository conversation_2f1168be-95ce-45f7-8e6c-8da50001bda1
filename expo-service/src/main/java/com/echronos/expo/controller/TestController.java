package com.echronos.expo.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.cache.MapCache;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.handler.AbstractSheetWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.echronos.commons.Result;
import com.echronos.expo.dto.AudienceExcelImportDTO;
import com.echronos.expo.enums.ExportTypeEnum;
import com.echronos.expo.param.ExpoAudienceImportParam;
import com.echronos.expo.util.excel.AudienceExcelImportListener;
import com.echronos.expo.util.excel.I18nCellWriteHandler;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-08-23 17:19
 */
@RestController
@RequestMapping("test")
public class TestController {

    @Resource
    private HttpServletResponse response;

    @PostMapping(value = "download")
    public void exportData() throws IOException {
        setDownResponse(response, ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc() + ExcelTypeEnum.XLSX.getValue());

        List<List<String>> dynamicHead = new ArrayList<>();
        dynamicHead.add(Arrays.asList("name","*Name"));
        dynamicHead.add(Arrays.asList("mobile","Mobile"));
        dynamicHead.add(Arrays.asList("e-mail","E-mail"));
        dynamicHead.add(Arrays.asList("channel","*Channel"));
        dynamicHead.add(Arrays.asList("channel","*Channel"));
        dynamicHead.add(Arrays.asList("channel","Would you like to register as a professional buyer?"));
        dynamicHead.add(Arrays.asList("channel",""));

        dynamicHead.add(Arrays.asList("",""));

        dynamicHead.add(Arrays.asList("colum1","表单列1"));
        dynamicHead.add(Arrays.asList("colum2","表单列2"));

        List<List<String>> valueList = new ArrayList<>();
//        valueList.add(Arrays.asList("姓名","年龄","表单列1","表单列2"));

        EasyExcel.write(response.getOutputStream())
                .head(dynamicHead)
                .inMemory(true)
                .registerWriteHandler(new RowWriteHandler() {
                    @Override
                    public void afterRowCreate(RowWriteHandlerContext context) {
                        Row row = context.getRow();
                        // 检查是否为第一行（索引为0）
                        if (row.getRowNum() == 0) {
                            // 隐藏第一行
                            row.setZeroHeight(true);
                        }
                    }
                })
                .registerWriteHandler(new I18nCellWriteHandler())
                .sheet(ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc())
                .doWrite(valueList);
    }

    @PostMapping(value = "read")
    public Result readData(ExpoAudienceImportParam param) throws IOException {
        InputStream inputStream = param.getFile().getInputStream();
        AudienceExcelImportListener listener = new AudienceExcelImportListener();
        // 使用 EasyExcel 动态读取数据
        EasyExcel.read(inputStream)
                .sheet(0) // 读取第一个 sheet
                .headRowNumber(0) // 表头在第0行
                .registerReadListener(listener)
                .doRead();
        return Result.build();
    }


    private void setDownResponse(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        // 响应到页面下载，配置文件下载
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        // 下载文件能正常显示中文
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }

}
