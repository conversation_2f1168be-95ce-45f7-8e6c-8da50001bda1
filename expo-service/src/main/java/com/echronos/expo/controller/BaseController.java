package com.echronos.expo.controller;


import com.echronos.commons.enums.ErrorMsgEnum;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.commons.utils.RequestUserUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * base controller
 */
@Component
public class BaseController {
    /**
     * 获取用户ID
     *
     * @return userId
     */
    public Integer getUserId() {
        return RequestUserUtils.getUser().getId();
    }

    /**
     * 获取公司ID
     *
     * @return companyId
     */
    public Integer getCompanyId() {
        return RequestUserUtils.getUser().getCompanyId();
    }

    /**
     * 获取租户ID
     *
     * @return tenantId
     */
    public String getTenantId() {
        return AppThreadLocal.getTenantId();
    }

    /**
     * 获取成员ID
     *
     * @return memberId
     */
    public Integer getMemberId() {
        return RequestUserUtils.getUser().getMemberId();
    }

    /**
     * 校验权限
     */
    public void checkPermission(String permissionCode) {
        Set<?> permission = RequestUserUtils.getPermission();
        boolean b = permission.contains(permissionCode);
        if (!b) {
            throw new BusinessException(ErrorMsgEnum.NO_PERM.getErrorCode(), ErrorMsgEnum.NO_PERM.getMsg());
        }
    }
}

