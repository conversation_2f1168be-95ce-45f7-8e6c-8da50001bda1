package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.expo.param.ExpoAudienceSignDateParam;
import com.echronos.expo.param.IdParam;
import com.echronos.expo.service.ExpoStatisticsService;
import com.echronos.expo.vo.statistics.ExpoAudienceChannelVO;
import com.echronos.expo.vo.statistics.ExpoAudienceDateCountVO;
import com.echronos.expo.vo.statistics.ExpoAudienceSignNotSignInRateVO;
import com.echronos.expo.vo.statistics.ExpoAudienceStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展会首页统计报表
 * <AUTHOR>
 * @date 2025-08-20 16:33
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/info/statistics")
public class ExpoInfoStatisticsController {

    @Resource
    private ExpoStatisticsService expoStatisticsService;

    /**
     * 展会观众维度信息统计
     * @return
     */
    @GetMapping(value = "/audience/count")
    public Result<ExpoAudienceStatisticsVO> getExpoAudienceStatistics(IdParam param){
        return Result.build(expoStatisticsService.getExpoAudienceStatistics(param.getId()));
    }

    /**
     * 获取展会观众展位签到未签比率
     * @param param
     * @return
     */
    @GetMapping(value = "/audience/sign/in/count")
    public Result<ExpoAudienceSignNotSignInRateVO> getExpoAudienceSignInNotSignInCount(IdParam param){
        return Result.build(expoStatisticsService.getExpoAudienceSignInNotSignInCount(param.getId()));
    }

    /**
     * 获取展会观众渠道统计
     * @param param
     * @return
     */
    @GetMapping(value = "/audience/channel/count")
    public Result<List<ExpoAudienceChannelVO>> getExpoAudienceChannelStatistics(IdParam param){
        return Result.build(expoStatisticsService.getExpoAudienceChannelList(param.getId()));
    }

    /**
     * 获取展会观众签到日统计
     * @param param
     * @return
     */
    @GetMapping(value = "/audience/sign/in/day/count")
    public Result<List<ExpoAudienceDateCountVO>> getAudienceSingInDayCount(IdParam param){
        return Result.build(expoStatisticsService.getAudienceSingInDayCount(param.getId()));
    }

    /**
     * 获取展会观众签到时间段统计
     * @param param
     * @return
     */
    @GetMapping(value = "/audience/sign/in/time/count")
    public Result<List<ExpoAudienceDateCountVO>> getAudienceSingInTimeCount(ExpoAudienceSignDateParam param){
        return Result.build(expoStatisticsService.getAudienceSingInTimeCount(param.getExpoId(), param.getTime()));
    }

}
