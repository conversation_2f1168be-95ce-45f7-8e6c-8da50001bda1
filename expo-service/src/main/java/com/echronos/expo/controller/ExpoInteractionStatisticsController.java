package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.page.PageVO;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.param.*;
import com.echronos.expo.param.appoint.AppointDetailListParam;
import com.echronos.expo.service.IExpoInteractionStatisticsService;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.statistics.ExpoAppointDetailCountVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailListVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * 展会互动数据统计
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@RestController
@RequestMapping("v1/expo/interaction/statistics")
public class ExpoInteractionStatisticsController {

    @Resource
    private IExpoInteractionStatisticsService expoInteractionStatisticsService;

    /**
     * 展会互动次数统计
     *
     * @param param 统计参数
     * @return 互动次数统计数据
     */
    @GetMapping(value = "/total")
    public Result<ExpoInteractionStatisticsVO> interactionStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(param.getExpoId());
        return Result.build(expoInteractionStatisticsService.interactionStatistics(dto));
    }

    /**
     * 预约状态分布统计
     *
     * @param param 统计参数
     * @return 预约状态分布数据
     */
    @GetMapping(value = "/appointment/status")
    public Result<ExpoAppointStatusVO> appointmentStatusDistributionStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(param.getExpoId());
        return Result.build(expoInteractionStatisticsService.appointmentStatusStatistics(dto));
    }

    /**
     * 每日互动趋势统计
     *
     * @param param 统计参数
     * @return 每日互动趋势数据
     */
    @GetMapping(value = "/daily/trend")
    public Result<List<ExpoInteractionTrendVO>> interactionTrendStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(expoInteractionStatisticsService.interactionTrendStatistics(dto));
    }

    /**
     * 获取展会日期列表
     *
     * @param param 统计参数
     * @return 展会日期列表
     */
    @GetMapping(value = "/dates")
    public Result<List<LocalDate>> getExpoDates(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(param.getExpoId());
        return Result.build(expoInteractionStatisticsService.getExpoDates(dto));
    }

    /**
     * 每小时互动分布统计
     *
     * @param param 统计参数
     * @return 每小时互动分布数据
     */
    @GetMapping(value = "/hourly")
    public Result<List<ExpoHourlyInteractionVO>> hourlyInteractionStatistics(ExpoHourlyStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(param.getExpoId());
        dto.setDate(LocalDate.parse(param.getDate()));
        return Result.build(expoInteractionStatisticsService.hourlyInteractionStatistics(dto));
    }

    /**
     * 互动展商排行统计
     *
     * @param param 统计参数
     * @return 互动展商排行数据
     */
    @GetMapping(value = "/exhibitor/ranking")
    public Result<List<ExpoExhibitorInteractionRankingVO>> exhibitorInteractionRankingStatistics(ExpoStatisticsPageParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        ExpoPageVO<ExpoExhibitorInteractionRankingVO> expoPageVO = expoInteractionStatisticsService.exhibitorInteractionRankingStatistics(dto);
        return Result.build(expoPageVO.getRecords(), expoPageVO.getTotalElements());
    }

    /**
     * 展商扫码详情次数总计
     *
     * @param param 统计参数
     * @return 展商扫码详情统计数据
     */
    @GetMapping(value = "/detail/total")
    public Result<ExpoExhibitorScanCountVO> exhibitorScanDetailTotal(ExpoExhibitorStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        return Result.build(expoInteractionStatisticsService.exhibitorScanDetailTotal(dto));
    }

    /**
     * 展商扫码记录列表
     *
     * @param param 统计参数
     * @return 展商扫码记录列表
     */
    @GetMapping(value = "/record/list")
    public Result<List<ExpoExhibitorScanListVO>> exhibitorScanRecordList(ExpoStatisticsPageParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        ExpoPageVO<ExpoExhibitorScanListVO> pageVO = expoInteractionStatisticsService.exhibitorScanRecordList(dto);
        return Result.build(pageVO.getRecords(), pageVO.getTotalElements());
    }

    /**
     * 展商扫码观众详情列表
     *
     * @param param 统计参数
     * @return 展商扫码观众详情列表
     */
    @GetMapping(value = "/audience/list")
    public Result<List<ExpoExhibitorScanAudienceVO>> exhibitorScanAudienceList(ExpoExhibitorStatisticsPageParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        ExpoPageVO<ExpoExhibitorScanAudienceVO> pageVO = expoInteractionStatisticsService.exhibitorScanAudienceList(dto);
        return Result.build(pageVO.getRecords(), pageVO.getTotalElements());
    }

    /**
     * 预约详情预约数统计
     *
     * @param param 参数
     * @return ExpoAppointDetailCountVO
     */
    @GetMapping(value = "/appoint/detail/count")
    public Result<ExpoAppointDetailCountVO> appointDetailCount(ExpoStatisticsParam param) {
        ExpoAppointmentDTO dto = new ExpoAppointmentDTO();
        dto.setExpoId(param.getExpoId());
        return Result.build(expoInteractionStatisticsService.appointDetailCount(dto));
    }

    /**
     * 预约详情预约列表
     *
     * @param param 参数
     * @return List<ExpoAppointDetailListVO>
     */
    @GetMapping(value = "/appoint/detail/list")
    public Result<List<ExpoAppointDetailListVO>> appointDetailList(AppointDetailListParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        PageVO<ExpoAppointDetailListVO> pageVO = expoInteractionStatisticsService.appointDetailList(dto);
        return Result.build(pageVO.getList(), (long) pageVO.getTotal());
    }

    /**
     * 预约详情
     *
     * @param param 参数
     * @return ExpoAppointDetailVO
     */
    @GetMapping(value = "/appoint/detail")
    public Result<ExpoAppointDetailVO> appointDetail(IdParam param) {
        ExpoAppointmentDTO dto = new ExpoAppointmentDTO();
        dto.setId(param.getId());
        return Result.build(expoInteractionStatisticsService.appointDetail(dto));
    }

    /**
     * 互动分析报表统计
     * @param param 统计参数
     * @return 互动分析报表数据
     */
    @GetMapping(value = "/report")
    public Result<ExpoInteractionReportVO> report(ExpoStatisticsDateParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(expoInteractionStatisticsService.report(dto));
    }
}
