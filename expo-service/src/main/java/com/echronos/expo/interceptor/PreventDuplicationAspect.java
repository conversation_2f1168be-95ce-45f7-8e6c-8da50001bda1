package com.echronos.expo.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.exception.ParamsValidateException;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.annotation.PreventDuplication;
import com.echronos.expo.constants.RedisConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.DigestUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Objects;

import static cn.hutool.core.lang.Validator.UUID;

/**
 * <AUTHOR>
 * @date 2024/1/22
 */

@Aspect
@Component
@Slf4j
public class PreventDuplicationAspect {

    /**
     * lua
     */
    private static final String RELEASE_LOCK_LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    /**
     * 定义切点
     */
    @Pointcut("@annotation(com.echronos.expo.annotation.PreventDuplication)")
    public void preventDuplication() {
    }

    /**
     * 请求前后的处理
     */
    @Around("preventDuplication()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Assert.notNull(request, "request cannot be null.");
        // 获取执行方法
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        // 获取防重复提交注解
        PreventDuplication annotation = method.getAnnotation(PreventDuplication.class);
        // 获取用户Id
        Integer userId = RequestUserUtils.getUser().getId();
        // 获取redisKey
        String lockKey = String.format(RedisConstant.PREVENT_DUPLICATION_PREFIX, userId, annotation.module(), getMethodSign(method, joinPoint.getArgs()));
        try {
            // 获取锁
            boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, UUID, annotation.expire(), annotation.timeUnit());
            if (!success) {
                throw new ParamsValidateException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode(), annotation.msg());
            }
            return joinPoint.proceed();
        } finally {
            if(StringUtils.isNotBlank(lockKey)){
                // 最后记得释放锁
                DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(RELEASE_LOCK_LUA_SCRIPT, Long.class);
                redisTemplate.execute(redisScript, Collections.singletonList(lockKey), UUID);
            }
        }
    }

    /**
     * 生成方法标记：采用数字签名算法SHA1对方法签名字符串加签
     *
     */
    private String getMethodSign(Method method, Object... args) {
        StringBuilder sb = new StringBuilder(method.toString());
        for (Object arg : args) {
            String argStr = toString(arg);
            try {
                // 查出来需要忽略的值，替换成null
                JSONObject o = JSONObject.parseObject(argStr);
                argStr = JSONObject.toJSONString(o);
            } catch (Exception e) {
                log.info("参数无法转成JSON");
            }
            sb.append(argStr);
        }
        return DigestUtils.sha1DigestAsHex(sb.toString());
    }

    private String toString(Object arg) {
        if (Objects.isNull(arg)) {
            return "null";
        }
        if (arg instanceof Number) {
            return arg.toString();
        }
        return JSONObject.toJSONString(arg);
    }

}
