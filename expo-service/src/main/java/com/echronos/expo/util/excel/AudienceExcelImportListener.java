package com.echronos.expo.util.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.echronos.expo.dto.AudienceExcelImportDTO;
import com.echronos.expo.util.AudienceTemplateFiled;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-08-26 16:34
 */
@Slf4j
public class AudienceExcelImportListener extends AnalysisEventListener<Map<Integer, String>> {

    public List<AudienceExcelImportDTO> dataList = new ArrayList<>();
    // 列对应的字段名
    public Map<Integer, String> columnFieldMap = new HashMap<>();

    @Override
    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
        if (context.readRowHolder().getRowIndex() == 0) {
            // 第一行是表头
            columnFieldMap.putAll(rowData);
            log.info("读取到表头：" + rowData);
        } else if ( context.readRowHolder().getRowIndex() > 1){
            // 根据列读取数据
            AudienceExcelImportDTO dto = new AudienceExcelImportDTO();
            Map<String, String> extMap = new HashMap<>();
            for (int i = 0; i < columnFieldMap.size(); i++) {
                String value = rowData.getOrDefault(i, null);
                String columnField = columnFieldMap.get(i);
                if("name".equals(columnField)){
                    dto.setName(value);
                } else if("phone".equals(columnField)){
                    dto.setPhone(value);
                } else if("email".equals(columnField)){
                    dto.setEmail(value);
                } else if("channelName".equals(columnField)){
                    dto.setChannelName(value);
                } else if("companyName".equals(columnField)){
                    dto.setCompanyName( value);
                } else if("companyWebsite".equals(columnField)){
                    dto.setCompanyWebsite(value);
                } else if("ip".equals(columnField)){
                    dto.setIp(value);
                } else if("createTime".equals(columnField)){
                    dto.setCreateTimeText(value);
                } else{
                    // 自定义表单数据
                    extMap.put(columnField, value);
                }
            }
            // 数据行
            dto.setExtMap(extMap);
            dataList.add(dto);
            log.info("读取到数据行：" + dto);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel 所有数据读取完成：{}", JSON.toJSONString(dataList));
    }

    // 可选：处理表头
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }

    public List<AudienceExcelImportDTO> getDataList() {
        return dataList;
    }

    public Map<Integer, String> getColumnFieldMap() {
        return columnFieldMap;
    }

}
