/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoExhibitorScanRecordDTO;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import com.echronos.expo.model.ext.ExpoExhibitorScanCountExt;
import com.echronos.expo.model.ext.ExpoExhibitorScanRecordExt;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * ExpoExhibitorScanRecord Dao
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface ExpoExhibitorScanRecordDao extends BaseMapper<ExpoExhibitorScanRecord> {

    /**
     * 按日期统计展商扫码次数
     *
     * @param expoId 展会ID
     * @return 日期和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByDate(@Param("expoId") Integer expoId);

    /**
     * 按小时统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param statisticsDate 统计日期
     * @return 小时和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByHour(@Param("expoId") Integer expoId, @Param("statisticsDate") LocalDate statisticsDate);

    /**
     * 按展商统计扫码次数
     *
     * @param expoId 展会ID
     * @return 展商ID和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByExhibitor(@Param("expoId") Integer expoId);

    /**
     * 分页查询展商扫码记录
     *
     * @param page 分页对象
     * @param dto 参数DTO
     * @return 展商扫码记录列表
     */
    List<ExpoExhibitorScanRecordExt> pageExhibitorScanRecordList(Page<ExpoExhibitorScanRecordDTO> page, @Param("dto") ExpoExhibitorScanRecordDTO dto);

    /**
     * 分页查询展商扫码观众
     *
     * @param page 分页对象
     * @param dto 参数DTO
     * @return 展商扫码观众列表
     */
    List<ExpoExhibitorScanRecordDTO> pageExhibitorScanAudienceList(Page<ExpoExhibitorScanRecordDTO> page, @Param("dto") ExpoExhibitorScanRecordDTO dto);
}
