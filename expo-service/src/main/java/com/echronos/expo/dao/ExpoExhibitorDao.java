/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.model.ExpoExhibitor;
import com.echronos.expo.model.ext.ExpoExhibitorExt;
import com.echronos.expo.model.ext.ExpoExhibitorRankingExt;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import com.echronos.expo.model.ext.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * ExpoExhibitor Dao
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface ExpoExhibitorDao extends BaseMapper<ExpoExhibitor> {

    /**
     * 分页查询
     * @param page 分页对象
     * @param dto 参数DTO
     * @return
     */
    List<ExpoExhibitorDTO> pageList(Page page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 建站分页查询展商信息
     *
     * @return
     */
    IPage<ExpoExhibitorExt> webExhibitorPage(Page<ExpoExhibitorDTO> page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 官网分页查询展商信息
     * @param page
     * @param dto
     * @return
     */
    IPage<ExpoExhibitorExt> queryAllExhibitorList(Page<ExpoExhibitorDTO> page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 获取首页展商数量
     * @param companyId 公司ID
     * @return
     */
    ExpoIndexCountExt getIndexExhibitorCount(@Param("companyId") Integer companyId);

    /**
     * 获取首页展商分析数据
     * @param companyId 公司ID
     * @return
     */
    ExpoIndexExhibitorAnalyzeExt getIndexExhibitorAnalyzeCount(@Param("companyId") Integer companyId);

    /**
     * 获取首页展商分析频率数据
     * @param companyId
     * @return
     */
    ExpoIndexExhibitorAnalyzeFrequencyExt getIndexExhibitorAnalyzeFrequencyCount(@Param("companyId") Integer companyId);

    /**
     * 获取首页展商分析详情列表
     * @param page
     * @param dto
     * @return
     */
    List<ExpoIndexExhibitorAnalyzeDetailExt> getIndexExhibitorAnalyzeDetailList(Page page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 获取展商互动排行统计
     *
     * @param dto 统计参数
     * @return 展商排行列表
     */
    List<ExpoExhibitorRankingExt> getExhibitorInteractionRanking(@Param("dto") ExpoInteractionStatisticsDTO dto);

    /**
     * 获取展商互动排行总数
     * @param expoId 展会ID
     * @return 总数
     */
    Integer getExhibitorInteractionRankingCount(@Param("expoId") Integer expoId);
}
