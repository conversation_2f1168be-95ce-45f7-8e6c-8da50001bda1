/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoAppointedPersonnelDTO;
import com.echronos.expo.model.ExpoAppointedPersonnel;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * ExpoAppointedPersonnel Dao
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface ExpoAppointedPersonnelDao extends BaseMapper<ExpoAppointedPersonnel> {

    /**
     * 查询已设置可预约人员
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnel>
     */
    List<ExpoAppointedPersonnel> queryList(@Param("dto") ExpoAppointedPersonnelDTO dto);

    /**
     * 查询已设置可预约人员
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnel>
     */
    List<ExpoAppointedPersonnel> queryCanAppointedList(@Param("dto") ExpoAppointedPersonnelDTO dto);

    /**
     * 查询可预约人员信息
     *
     * @param dto
     * @return
     */
    ExpoAppointedPersonnelDTO queryInfo(@Param("dto") ExpoAppointedPersonnelDTO dto);
}
