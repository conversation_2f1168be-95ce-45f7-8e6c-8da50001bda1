/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.model.ExpoAppointment;
import com.echronos.expo.model.ext.ExpoAppointmentCountExt;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;


/**
 * ExpoAppointment Dao
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface ExpoAppointmentDao extends BaseMapper<ExpoAppointment> {

    /**
     * 查询预约我的
     *
     * @param dto
     * @return
     */
    List<ExpoAppointment> queryAppointMyList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 展会预约时间列表
     *
     * @param dto 参数
     * @return List<ExpoAppointmentExt>
     */
    List<ExpoAppointmentExt> queryAppointTimeList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    ExpoAppointmentExt queryDetail(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 查询48小时内未响应的预约
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    List<ExpoAppointmentExt> queryNoResponse(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 查询展前24H未响应的预约
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    List<ExpoAppointmentExt> queryBeforeExpoNoResponse(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 按日期统计预约次数
     *
     * @param expoId 展会ID
     * @return 日期和预约次数的映射
     */
    List<ExpoAppointmentCountExt> countAppointmentsByDate(@Param("expoId") Integer expoId);

    /**
     * 按小时统计预约次数 (基于预约时间表的开始时间)
     *
     * @param expoId 展会ID
     * @param statisticsDate 统计日期
     * @return 小时和预约次数的映射
     */
    List<ExpoAppointmentCountExt> countAppointmentsByHour(@Param("expoId") Integer expoId, @Param("statisticsDate") LocalDate statisticsDate);

    /**
     * 按展商统计预约次数 (统计被预约的展商)
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 展商ID和预约次数的映射
     */
    List<ExpoAppointmentCountExt> countAppointmentsByExhibitor(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);

    /**
     * 预约详情预约数统计
     *
     * @param expoId 展会ID
     * @return ExpoAppointmentCountExt
     */
    ExpoAppointmentCountExt queryDetailCount(@Param("expoId") Integer expoId);

    /**
     * 展商预约观众列表
     *
     * @param dto 预约参数
     * @return 预约列表
     */
    List<ExpoAppointmentExt> queryAudienceAppointList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 观众预约展商列表
     *
     * @param dto 预约参数
     * @return 预约列表
     */
    List<ExpoAppointmentExt> queryExhibitorAppointList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 查询统计预约详情
     *
     * @param id
     * @return
     */
    ExpoAppointmentExt queryStatisticsDetail(@Param("id") Integer id);
}
