package com.echronos.expo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-08-18 14:06
 */
public interface ExpoChannelEnum {

    @Getter
    enum ChannelType {
        AUDIENCE(1, "观众"),
        EXHIBITOR(2, "展商");

        private final Integer code;
        private final String msg;

        ChannelType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

    }

    @Getter
    enum ChannelSource {
        SYSTEM(1, "系统"),
        EXHIBITOR(2, "展商");

        private final Integer code;
        private final String msg;

        ChannelSource(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

    }

}
