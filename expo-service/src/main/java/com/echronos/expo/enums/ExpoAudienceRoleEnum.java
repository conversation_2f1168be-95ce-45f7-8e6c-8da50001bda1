package com.echronos.expo.enums;

import com.echronos.expo.vo.ExpoCodeValueVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/26 17:39
 */
@Getter
@AllArgsConstructor
public enum ExpoAudienceRoleEnum {

    GENERAL_AUDIENCE(1, "普通观众"),
    PROFESSIONAL_BUYERS(2, "专业买家"),
    ALL_AUDIENCE(3, "全部观众");

    private final Integer code;
    private final String name;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static ExpoAudienceRoleEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ExpoAudienceRoleEnum audienceRoleEnum : values()) {
            if (audienceRoleEnum.code.equals(code)) {
                return audienceRoleEnum;
            }
        }
        return null;
    }

    /**
     * 获取vo
     *
     * @return
     */
    public static List<ExpoCodeValueVO> getVO() {
        List<ExpoCodeValueVO> list = new ArrayList<>();
        for (ExpoAudienceRoleEnum audienceRoleEnum : values()) {
            ExpoCodeValueVO vo = new ExpoCodeValueVO();
            vo.setCode(String.valueOf(audienceRoleEnum.code));
            vo.setName(audienceRoleEnum.name);
            list.add(vo);
        }
        return list;
    }
}
