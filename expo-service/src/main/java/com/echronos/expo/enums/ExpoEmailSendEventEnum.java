package com.echronos.expo.enums;

import com.echronos.expo.vo.ExpoCodeValueVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/26 17:32
 */
@Getter
@AllArgsConstructor
public enum ExpoEmailSendEventEnum {

    AUDIENCE_REGISTER(1, "观众注册", TriggerTypeEnum.IMMEDIATE),
    BEFORE_EVENT_DAYS(2, "开展前（天数）", TriggerTypeEnum.ADVANCE),
    AFTER_EVENT_DAYS(3, "展会结束后（天数）", TriggerTypeEnum.DELAY),
    BECOME_EXHIBITOR(4, "成为展商", TriggerTypeEnum.IMMEDIATE);

    private final Integer code;
    private final String name;
    private final TriggerTypeEnum triggerType;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static ExpoEmailSendEventEnum getByCode(Integer code) {
        for (ExpoEmailSendEventEnum sendEventEnum : values()) {
            if (sendEventEnum.code.equals(code)) {
                return sendEventEnum;
            }
        }
        return null;
    }

    /**
     * 获取vo
     *
     * @return
     */
    public static List<ExpoCodeValueVO> getVO() {
        List<ExpoCodeValueVO> list = new ArrayList<>();
        for (ExpoEmailSendEventEnum sendEventEnum : values()) {
            ExpoCodeValueVO vo = new ExpoCodeValueVO();
            vo.setCode(String.valueOf(sendEventEnum.code));
            vo.setName(sendEventEnum.name);
            list.add(vo);
        }
        return list;
    }
}
