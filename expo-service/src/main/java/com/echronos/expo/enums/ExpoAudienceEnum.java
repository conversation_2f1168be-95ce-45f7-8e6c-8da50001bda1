package com.echronos.expo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-08-23 16:58
 */
public interface ExpoAudienceEnum {

    @Getter
    enum IsBuyer {
        SELLER(0, "卖家"),
        BUYER(1, "买家");

        private final Integer code;
        private final String msg;

        IsBuyer(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

    }

}
