package com.echronos.expo.constants;

/**
 * <AUTHOR>
 * @Date 2025/5/15 11:04
 * @ClassName GatewayRoutingConstants
 */
public class GatewayRoutingConstants {
    static String serviceName = "/ech-expo";
    //观众注册表单二维码
    static String scanAudienceRegisterCode = "/expo/register?id=%d&expoId=%d&companyId=%d&memberId=%d";

    //观众签到二维码
    static String scanAudienceSignCode = "/expo/sign-in/success?id=%d&companyId=%d&expoId=%d";

    //展商邀请观众注册表单二维码
    static String scanExhibitorAudienceRegisterCode = "companyId=%d&expoId=%d&exhibitorId=%d";

    // 观众注册表单路径（站点）/system?expo=/expo/register-form?id=1&channelId=1&companyId=1
    public static String scanAudienceRegisterUrl = "/system?expo=";
    public static String scanAudienceRegisterUrlParam = "/expo/register-form?id=%d&channelId=%d&companyId=%d";

    // 跳转站点租赁页面路口
    public static String locationTenantLeaseUrl = "/system?exhibition=";
    public static String locationTenantLeaseUrlParam = "/rental-pao?expoId=%d";

    // 跳转扫码地址
    public static String locationCanQrUrl = "/ech-expo";

    /**
     * 观众注册表单二维码
     *
     * @return
     */
    public static String routingScanRegisterUrl() {
        return scanAudienceRegisterCode;
    }

    /**
     * 观众签到二维码
     *
     * @return
     */
    public static String scanAudienceSignCode() {
        return scanAudienceSignCode;
    }

    public static String getScanExhibitorAudienceRegisterCode() {
        return scanExhibitorAudienceRegisterCode;
    }
}
