package com.echronos.expo.schedule;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.commons.utils.TraceIdUtils;
import com.echronos.expo.service.IExpoService;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2025/5/14 15:17
 * @ClassName ExpoStatusTask
 */
@Slf4j
@ElasticSimpleJob(corn = "0 */1 * * * ?", disabled = false, overwrite = true)
public class ExpoStatusTask extends SimpleJobAbstract {

    @Resource
    private IExpoService expoService;

    @Override
    public void execute(ShardingContext shardingContext) {
        MDC.put(TraceIdUtils.TRACE_ID, UUID.randomUUID().toString());
//        log.info("批量更新展会状态开始!");
//        expoService.batchHandleStatus();
//        log.info("批量更新展会状态结束!");
    }
}
