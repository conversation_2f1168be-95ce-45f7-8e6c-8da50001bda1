package com.echronos.expo.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.commons.utils.TraceIdUtils;
import com.echronos.expo.manager.ExpoExhibitorEmailRecordsManager;
import com.echronos.expo.manager.ExpoExhibitorManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ExpoExhibitor;
import com.echronos.expo.model.ExpoExhibitorEmailRecords;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import com.echronos.nms.api.feign.IEMailFeign;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展商自动发送邮件
 * <AUTHOR>
 * @date 2025-08-22 15:03
 */
@Slf4j
@ElasticSimpleJob(corn = "0 0/10 * * * ?", disabled = false, overwrite = true)
public class ExhibitorAutoSendEmailTask extends SimpleJobAbstract {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoExhibitorEmailRecordsManager expoExhibitorEmailRecordsManager;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private IEMailFeign ieMailFeign;

    @Override
    public void execute(ShardingContext shardingContext) {
        if(1== 1){
            return;
        }
        //填充默认数据源
        AppThreadLocal.setTenantId("334702cc-c8a1-4885-a5ec-97f81eecdc9b");
        AppThreadLocal.setSourceCode("hsj");
        // 填充TraceId
        MDC.put(TraceIdUtils.TRACE_ID, UUID.randomUUID().toString());
        Integer pageNo = 1;
        Integer pageSize = 5;
        log.info("开始执行自动发送展商邮件");
        while (true){
            Page<ExpoInfo> expoInfoPage = expoInfoManager.getExpoInfoByPage(pageNo, pageSize);
            if (CollectionUtil.isEmpty(expoInfoPage.getRecords())){
                log.info("结束执行自动发送展商邮件");
                return;
            }
            // todo 批量根据展会ID获取展会配置的邮件自动发送规则
            List<Integer> expoIdList = expoInfoPage.getRecords().stream().map(expoInfo -> expoInfo.getId()).collect(Collectors.toList());
            List<Integer> expoAutoSendRuleIdList = new ArrayList<>();
            if(CollectionUtil.isEmpty(expoAutoSendRuleIdList)){
                log.info("没有配置邮件自动发送规则展会expoIds={}", expoIdList);
                pageNo = pageNo + 1;
                continue;
            }
            // 展会对应的邮件自动发送规则
            Map<Integer, List<Object>> expoAutoSendRuleMap = new HashMap<>();

            // 遍历展会开始推送邮件
            for(ExpoInfo expoInfo : expoInfoPage.getRecords()){
                List<Object> ruleList = expoAutoSendRuleMap.get(expoInfo);
                if(CollectionUtil.isNotEmpty(ruleList)){
                    Integer exhibitorPageNo = 1;
                    Integer exhibitorPageSize = 200;
                    while (true){
                        // 分页查询出展商
                        Page<ExpoExhibitor> exhibitorPage = expoExhibitorManager.getExpoExhibitorByPage(exhibitorPageNo, exhibitorPageSize, expoInfo.getId());
                        List<ExpoExhibitor> exhibitorList = exhibitorPage.getRecords();
                        if(CollectionUtil.isEmpty(exhibitorList)){
                            break;
                        }

                        // 获取这批展商已经自动发送过的记录
                        List<Integer> exhibitorIdList = exhibitorList.stream().map(expoExhibitor -> expoExhibitor.getId()).collect(Collectors.toList());
                        List<ExpoExhibitorEmailRecords> exhibitorEmailRecordsList = expoExhibitorEmailRecordsManager
                                .getAutoSendList(exhibitorIdList, expoAutoSendRuleIdList);
                        // Map<展商ID，Map<规则ID，发送记录>
                        Map<Integer, Map<Integer, ExpoExhibitorEmailRecords>> allExhibitorEmailRecordsMap = new HashMap<>();
                        if(CollectionUtil.isNotEmpty(exhibitorEmailRecordsList)){
                            allExhibitorEmailRecordsMap = exhibitorEmailRecordsList.stream()
                                    .collect(Collectors.groupingBy(
                                            ExpoExhibitorEmailRecords::getExhibitorId,
                                            Collectors.toMap(
                                                    ExpoExhibitorEmailRecords::getAutoRuleId,
                                                    record -> record,
                                                    (existing, replacement) -> replacement // 如果有重复 key，保留新的
                                            )
                                    ));
                        }

                        // 遍历展商开始发送邮件
                        for(ExpoExhibitor exhibitor : exhibitorList){
                            Map<Integer, ExpoExhibitorEmailRecords> exhibitorEmailRecordMap = allExhibitorEmailRecordsMap.get(exhibitor.getId());
                            for(Object rule : ruleList){
                                // 规则ID
                                Integer ruleId = 1;
                                if(null != exhibitorEmailRecordMap && null != exhibitorEmailRecordMap.get(ruleId)){
                                    // 该展商该自动发送规则邮件已经发送过了
                                    continue;
                                }
                                LocalDateTime now = LocalDateTime.now();
                                // 邮件规则触发事件
                                Integer ruleEven = null;
                                // 规则提前天数
                                Integer advanceDay = null;
                                // 规则延后天数
                                Integer delayDays = null;
                                // 规则发送时间
                                String sendTimeStr = "11:15:00";
                                LocalTime sendTime = LocalTime.parse(sendTimeStr);
                                if(1 == ruleEven){
                                    // 成为展商即发送

                                } else if(2 == ruleEven && expoInfo.getZoneIdStartTime().compareTo(LocalDateTime.now()) == 0){
                                    // 开展前多少天即发送
                                    LocalDateTime advanceTime = expoInfo.getZoneIdStartTime().minusDays(advanceDay).with(sendTime);
                                    if(now.compareTo(advanceTime) < 0){
                                        // 未到达发送时间
                                        continue;
                                    }
                                } else if(3 == ruleEven && expoInfo.getZoneIdEndTime().compareTo(LocalDateTime.now()) == 0){
                                    // 开展后多少天即发送
                                    LocalDateTime delayTime = expoInfo.getZoneIdEndTime().plusDays(delayDays).with(sendTime);
                                    if(now.compareTo(delayTime) < 0){
                                        // 未到达发送时间
                                        continue;
                                    }
                                }

                                // 手动事务
                                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
                                def.setTimeout(60);
                                TransactionStatus transactionStatus = null;
                                try {
                                    // todo 发送邮件
//                                    HashMap<String, String> paramMap = new HashMap<>();
//                                    paramMap.put("userName", r.getName());
//                                    paramMap.put("companyName", r.getCompanyName());
//                                    paramMap.put("QRcode", png);
//                                    SendEmailReq sendEmailReq = new SendEmailReq();
//                                    sendEmailReq.setTemplateId(dto.getTemplateId());
//                                    sendEmailReq.setSubject(dto.getSubject());
//                                    sendEmailReq.setContent(dto.getContent());
//                                    sendEmailReq.setConfigId(dto.getConfigId());
//                                    sendEmailReq.setCompanyId(r.getCompanyId());
//                                    sendEmailReq.setReceiver(r.getEmail());
//                                    sendEmailReq.setServiceName(ServiceNameEnum.ECH_EXPO.getName());
//                                    sendEmailReq.setTemplateParams(paramMap);
//                                    log.info("发送邮件req:{}", JSONObject.toJSONString(sendEmailReq));
//                                    Result<String> result = ieMailFeign.sendEmail(sendEmailReq);

                                    // 开启事务
                                    transactionStatus = dataSourceTransactionManager.getTransaction(def);
                                    ExpoExhibitorEmailRecords addRecords = new ExpoExhibitorEmailRecords();
                                    addRecords.setCompanyId(expoInfo.getCompanyId());
                                    addRecords.setExpoId(exhibitor.getExpoId());
                                    addRecords.setExhibitorId(exhibitor.getId());
                                    addRecords.setParamJson(null);
                                    addRecords.setBusinessCode(null);
                                    addRecords.setIsAutoSend(CommonStatus.YesOrNoEnum.YES.getValue());
                                    addRecords.setAutoRuleId(ruleId);
                                    addRecords.setCreateUser(1);
                                    addRecords.setCreateTime(LocalDateTime.now());
                                    expoExhibitorEmailRecordsManager.save(addRecords);
                                    //提交事务
                                    dataSourceTransactionManager.commit(transactionStatus);
                                } catch (Exception e) {
                                    log.error("TASK_EXHIBITOR_AUTO_SEND_EMAIL_ERROR:自动发送展商邮件错误: 展商id:{}, 规则id：{}, 错误信息:{}", exhibitor.getId(), ruleId, e);
                                    if(transactionStatus != null){
                                        // 回滚事务
                                        dataSourceTransactionManager.rollback(transactionStatus);
                                    }
                                }
                            }
                        }
                        exhibitorPageNo = exhibitorPageNo + 1;
                    }
                }
            }
            pageNo = pageNo + 1;
        }
    }


}
