package com.echronos.expo.schedule;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.commons.utils.TraceIdUtils;
import com.echronos.expo.service.IExpoAudienceService;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 观众信息同步至crm
 *
 * <AUTHOR>
 * @Date 2025/6/3 15:03
 * @ClassName AudienceCrmSyncTask
 */
@Slf4j
@ElasticSimpleJob(corn = "0 */1 * * * ?", disabled = false, overwrite = true)
public class AudienceCrmSyncTask extends SimpleJobAbstract {

    @Resource
    private IExpoAudienceService audienceService;


    @Override
    public void execute(ShardingContext shardingContext) {
        MDC.put(TraceIdUtils.TRACE_ID, UUID.randomUUID().toString());
//        log.info("批量同步观众到crm开始!");
//        audienceService.syncCrm();
//        log.info("批量同步观众到crm结束!");
    }
}
