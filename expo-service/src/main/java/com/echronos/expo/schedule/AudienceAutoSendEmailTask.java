package com.echronos.expo.schedule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.commons.Result;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.commons.utils.TraceIdUtils;
import com.echronos.expo.constants.GatewayRoutingConstants;
import com.echronos.expo.enums.ExpoAudienceEnum;
import com.echronos.expo.manager.ExpoAudienceEmailRecordsManager;
import com.echronos.expo.manager.ExpoAudienceManager;
import com.echronos.expo.manager.ExpoExhibitorEmailRecordsManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.*;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import com.echronos.nms.api.enums.EmailAutoSendEventEnum;
import com.echronos.nms.api.enums.ServiceNameEnum;
import com.echronos.nms.api.feign.IEMailFeign;
import com.echronos.nms.api.req.SendEmailReq;
import com.echronos.nms.api.resp.EmailTemplateResp;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 观众自动发送邮件
 * <AUTHOR>
 * @date 2025-08-23 15:27
 */
@Slf4j
@ElasticSimpleJob(corn = "0 0/10 * * * ?", disabled = false, overwrite = true)
public class AudienceAutoSendEmailTask extends SimpleJobAbstract {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoAudienceEmailRecordsManager expoAudienceEmailRecordsManager;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private IEMailFeign ieMailFeign;

    @Value("${remote.h5.domain}")
    private String gateway;

    @Override
    public void execute(ShardingContext shardingContext) {
        if(1== 1){
            return;
        }
        //填充默认数据源
        AppThreadLocal.setTenantId("334702cc-c8a1-4885-a5ec-97f81eecdc9b");
        AppThreadLocal.setSourceCode("hsj");
        // 填充TraceId
        MDC.put(TraceIdUtils.TRACE_ID, UUID.randomUUID().toString());
        Integer pageNo = 1;
        Integer pageSize = 5;
        log.info("开始执行自动发送观众邮件");
        while (true){
            Page<ExpoInfo> expoInfoPage = expoInfoManager.getExpoInfoByPage(pageNo, pageSize);
            if (CollectionUtil.isEmpty(expoInfoPage.getRecords())){
                log.info("结束执行自动发送观众邮件");
                return;
            }
            // todo 批量根据展会ID获取展会配置的邮件自动发送规则
            List<Integer> expoIdList = expoInfoPage.getRecords().stream().map(expoInfo -> expoInfo.getId()).collect(Collectors.toList());
            List<Integer> expoAutoSendRuleIdList = new ArrayList<>();
            if(CollectionUtil.isEmpty(expoAutoSendRuleIdList)){
                log.info("没有配置邮件自动发送规则展会expoIds={}", expoIdList);
                pageNo = pageNo + 1;
                continue;
            }
            // 展会对应的邮件自动发送规则
            Map<Integer, List<Object>> expoAutoSendRuleMap = new HashMap<>();

            // 遍历展会开始推送邮件
            for(ExpoInfo expoInfo : expoInfoPage.getRecords()){
                List<Object> ruleList = expoAutoSendRuleMap.get(expoInfo);
                if(CollectionUtil.isNotEmpty(ruleList)){
                    Integer audiencePageNo = 1;
                    Integer audiencePageSize = 200;
                    while (true) {
                        // 分页查询出观众
                        Page<ExpoAudience> audiencePage = expoAudienceManager.getExpoAudienceByPage(audiencePageNo, audiencePageSize, expoInfo.getId());
                        List<ExpoAudience> audienceList = audiencePage.getRecords();
                        if(CollectionUtil.isEmpty(audienceList)){
                            break;
                        }
                        // 获取这批展商已经自动发送过的记录
                        List<Integer> exhibitorIdList = audienceList.stream().map(audience -> audience.getId()).collect(Collectors.toList());
                        List<ExpoAudienceEmailRecords> audienceEmailRecordsList = expoAudienceEmailRecordsManager
                                .getAutoSendList(exhibitorIdList, expoAutoSendRuleIdList);
                        // Map<观众ID，Map<规则ID，发送记录>
                        Map<Integer, Map<Integer, ExpoAudienceEmailRecords>> allAudienceEmailRecordsMap = new HashMap<>();
                        if(CollectionUtil.isNotEmpty(audienceEmailRecordsList)){
                            allAudienceEmailRecordsMap = audienceEmailRecordsList.stream()
                                    .collect(Collectors.groupingBy(
                                            ExpoAudienceEmailRecords::getAudienceId,
                                            Collectors.toMap(
                                                    ExpoAudienceEmailRecords::getAutoRuleId,
                                                    record -> record,
                                                    (existing, replacement) -> replacement // 如果有重复 key，保留新的
                                            )
                                    ));
                        }

                        // 遍历观众开始发送邮件
                        for(ExpoAudience audience : audienceList){
                            Map<Integer, ExpoAudienceEmailRecords> audienceEmailRecordMap = allAudienceEmailRecordsMap.get(audience.getId());
                            for(Object rule : ruleList){
                                // 规则ID
                                Integer ruleId = 1;
                                if(null != audienceEmailRecordMap && null != audienceEmailRecordMap.get(ruleId)){
                                    // 该观众该自动发送规则邮件已经发送过了
                                    continue;
                                }
                                // 邮件规则触发事件
                                Integer ruleEven = null;
                                // 邮件规则收件观众角色
                                Integer ruleIsBuyerType = null;
                                // 观众注册邮件
                                if(1 == ruleEven){
                                    // 买家才发送
                                    if(1 == ruleIsBuyerType){
                                        if(ExpoAudienceEnum.IsBuyer.BUYER.getCode() != audience.getIsBuyer()){
                                            // 非买家不发送
                                            continue;
                                        }
                                    } else if(2 == ruleIsBuyerType){
                                        // 卖家
                                        if(ExpoAudienceEnum.IsBuyer.SELLER.getCode() != audience.getIsBuyer()){
                                            // 非卖家不发送
                                            continue;
                                        }
                                    }
                                }

                                // 手动事务
                                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
                                def.setTimeout(60);
                                TransactionStatus transactionStatus = null;
                                try {
                                    String scanAudienceSignCode = GatewayRoutingConstants.scanAudienceSignCode();
                                    String qrcodeUrl = String.format(gateway + scanAudienceSignCode, audience.getId(), expoInfo.getCompanyId(), expoInfo.getId());
                                    QrConfig qrConfig = new QrConfig(200, 200);
                                    String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
                                    HashMap<String, String> paramMap = new HashMap<>();
                                    paramMap.put("userName", audience.getName());
                                    paramMap.put("companyName", audience.getCompanyName());
                                    paramMap.put("QRcode", png);
                                    // 查询模板
                                    Result<EmailTemplateResp> emailTemplate = ieMailFeign.getEmailTemplate(EmailAutoSendEventEnum.AUDIENCE_REGISTER.getCode(),
                                            audience.getCompanyId());
                                    if (emailTemplate.getCode() != 0) {
                                        throw new BusinessException(emailTemplate.getCode(), emailTemplate.getMessage());
                                    }
                                    EmailTemplateResp templateData = emailTemplate.getData();
                                    SendEmailReq sendEmailReq = new SendEmailReq();
                                    sendEmailReq.setTemplateId(templateData.getId());
                                    sendEmailReq.setSubject(templateData.getSubject());
                                    sendEmailReq.setContent(templateData.getContent());
                                    sendEmailReq.setCompanyId(audience.getCompanyId());
                                    sendEmailReq.setReceiver(audience.getEmail());
                                    sendEmailReq.setServiceName(ServiceNameEnum.ECH_EXPO.getName());
                                    sendEmailReq.setTemplateParams(paramMap);
                                    Result<String> result = ieMailFeign.sendEmail(sendEmailReq);
                                    if (result.getCode() != 0) {
                                        log.error("自动发送观众邮件异常");
                                        throw new BusinessException(result.getCode(), result.getMessage());
                                    }
                                    // 开启事务
                                    transactionStatus = dataSourceTransactionManager.getTransaction(def);
                                    ExpoAudienceEmailRecords addRecords = new ExpoAudienceEmailRecords();
                                    addRecords.setCompanyId(expoInfo.getCompanyId());
                                    addRecords.setExpoId(audience.getExpoId());
                                    addRecords.setAudienceId(audience.getId());
                                    addRecords.setParamJson(null);
                                    addRecords.setBusinessCode(null);
                                    addRecords.setIsAutoSend(CommonStatus.YesOrNoEnum.YES.getValue());
                                    addRecords.setAutoRuleId(ruleId);
                                    addRecords.setCreateUser(1);
                                    addRecords.setCreateTime(LocalDateTime.now());
                                    expoAudienceEmailRecordsManager.save(addRecords);
                                    //提交事务
                                    dataSourceTransactionManager.commit(transactionStatus);
                                } catch (Exception e) {
                                    log.error("TASK_EXHIBITOR_AUTO_SEND_EMAIL_ERROR:自动发送观众邮件错误: 展商id:{}, 规则id：{}, 错误信息:{}", audience.getId(), ruleId, e);
                                    if(transactionStatus != null){
                                        // 回滚事务
                                        dataSourceTransactionManager.rollback(transactionStatus);
                                    }
                                }

                            }

                        }

                        audiencePageNo = audiencePageNo + 1;
                    }
                }
            }

        }
    }



}
