package com.echronos.expo.schedule;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.expo.constants.ExpoConstants;
import com.echronos.expo.dto.ExpoExhibitorBoothDTO;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.manager.ExpoAppointmentTimeManager;
import com.echronos.expo.manager.ExpoExhibitorBoothManager;
import com.echronos.expo.manager.FeignCommonManager;
import com.echronos.expo.model.ExpoAppointment;
import com.echronos.expo.model.ExpoAppointmentTime;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import com.echronos.system.resp.member.MemberSimpleResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.echronos.user.api.resp.member.BatchMemberInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 48H预约未响应通知任务
 *
 * <AUTHOR>
 * @date 2025/8/14 14:47
 */
@Slf4j
@ElasticSimpleJob(corn = "0 0/10 * * * ?", disabled = false, overwrite = true)
public class AppointmentNoticeTask extends SimpleJobAbstract {

    @Resource
    private ExpoAppointmentManager expoAppointmentManager;
    @Resource
    private ExpoExhibitorBoothManager expoExhibitorBoothManager;
    @Resource
    private ExpoAppointmentTimeManager expoAppointmentTimeManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("开始执行48H预约未响应通知任务");
        // 查询48小时内未响应的预约
        List<ExpoAppointmentExt> list = expoAppointmentManager.queryNoResponse();
        if (!CollectionUtils.isEmpty(list)) {
            AppThreadLocal.setTenantId(ExpoConstants.TENANT_ID);
            AppThreadLocal.setSourceCode(ExpoConstants.SOURCE_CODE);
            // 查询成员
            List<Integer> memberIds = list.stream()
                    .flatMap(obj -> Stream.of(obj.getMemberId(), obj.getAppointedMemberId()))
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, BatchMemberInfoResp> memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
            // 查询展位
            List<Integer> exhibitorIds = list.stream().map(ExpoAppointmentExt::getExhibitorId).distinct().collect(Collectors.toList());
            Map<Integer, List<ExpoExhibitorBoothDTO>> boothMap = expoExhibitorBoothManager.getDetailByExhibitorIdList(exhibitorIds);
            // 查询预约时间
            List<Integer> appointmentIds = list.stream().map(ExpoAppointment::getId).distinct().collect(Collectors.toList());
            List<ExpoAppointmentTime> appointmentTimeList = expoAppointmentTimeManager.queryByAppointmentId(appointmentIds);
            Map<Integer, List<ExpoAppointmentTime>> appointmentTimeMap = appointmentTimeList.stream().collect(Collectors.groupingBy(ExpoAppointmentTime::getAppointmentId));

            List<Integer> expoCompanyIds = list.stream().map(ExpoAppointmentExt::getExpoCompanyId).distinct().collect(Collectors.toList());
            // todo 根据权限查接收人
            Map<Integer, List<MemberSimpleResp>> map = feignCommonManager.queryMemberListBy(expoCompanyIds, "");
            // 查询公司信息
            Map<Integer, QueryCompanyResp> companyMap = feignCommonManager.queryCompanyByIds(expoCompanyIds);
            // todo 发im通知
            for (ExpoAppointmentExt expoAppointmentExt : list) {

            }
            // 更新发通知状态
            expoAppointmentManager.updateNoticeStatus(list.stream().map(ExpoAppointmentExt::getId).collect(Collectors.toList()));
        }
    }
}
