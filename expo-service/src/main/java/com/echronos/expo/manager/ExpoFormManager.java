/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoFormDao;
import com.echronos.expo.dto.FormSqlFieldDTO;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ExpoBooth;
import com.echronos.expo.model.ExpoForm;
import com.echronos.iform.api.entity.CollectFieldSort;
import com.echronos.iform.api.entity.CollectFilter;
import com.echronos.iform.sdk.utils.SqlUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;


/**
 * EchExpoForm Manager
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class ExpoFormManager extends ServiceImpl<ExpoFormDao, ExpoForm> {

    /**
     * 根据expoId、formGroup、formType查询表单
     * @return 表单
     */
    public ExpoForm getForm(Integer expoId, Integer formGroup, Integer formType){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getExpoId, expoId)
                .eq(ExpoForm::getFormGroup, formGroup)
                .eq(ExpoForm::getFormType, formType)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据id查询表单
     * @param id
     * @return
     */
    public ExpoForm getId(Integer id){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getId, id)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ids查询表单
     * @param expoIds
     * @return
     */
    public List<ExpoForm> getByExpoIds(List<Integer> expoIds){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .in(ExpoForm::getExpoId, expoIds)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 根据ids查询表单
     * @param ids
     * @return
     */
    public List<ExpoForm> getByIds(List<Integer> ids, Integer isEnable){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .in(ExpoForm::getId, ids)
                .eq(null != isEnable, ExpoForm::getIsEnable, isEnable)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 根据expoId查询表单
     * @param expoId
     * @return
     */
    public List<ExpoForm> getByExpoId(Integer expoId){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .in(ExpoForm::getId, expoId)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 根据formCode查询表单
     * @param formCode
     * @return
     */
    public ExpoForm getFormByFormCode(String formCode){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getFormCode, formCode)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }


    /**
     * 根据表单组表单配置
     * @param expoId 展会id
     * @param formGroup 表单组
     * @param isEnable 是否启用
     * @return
     */
    public List<ExpoForm> getFormListByFormGroup(Integer expoId, Integer formGroup, Integer isEnable){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getExpoId, expoId)
                .eq(ExpoForm::getFormGroup, formGroup)
                .eq(ExpoForm::getIsEnable,  isEnable)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 根据表单组表单配置
     * @param expoId 展会id
     * @param formType 表单类型
     * @param isEnable 是否启用
     * @return
     */
    public ExpoForm getFormByFormType(Integer expoId, Integer formType, Integer isEnable){
        LambdaQueryWrapper<ExpoForm> queryWrapper = new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getExpoId, expoId)
                .eq(ExpoForm::getFormType, formType)
                .eq(null != isEnable, ExpoForm::getIsEnable,  isEnable)
                .eq(ExpoForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 启用/编辑表单
     * @param id
     * @param isEnable
     * @param userId
     */
    public void updateIsEnable(Integer id, Integer isEnable, Integer userId){
        LambdaUpdateWrapper<ExpoForm> updateWrapper = new LambdaUpdateWrapper<ExpoForm>()
                .eq(ExpoForm::getId, id)
                .set(ExpoForm::getIsEnable, isEnable)
                .set(ExpoForm::getUpdateUser, userId)
                .set(ExpoForm::getUpdateTime, LocalDateTime.now());
        this.update(updateWrapper);
    }


    /**
     * 根据from表单查询拼接固定表头
     */
    public FormSqlFieldDTO getFromSqlFieldAndSort(String code, Integer companyId, List<CollectFilter> filters, List<CollectFieldSort> sortList){
        FormSqlFieldDTO dto = new FormSqlFieldDTO();
        //筛选条件
        String conditionScreen = SqlUtils.buildConditionNew(filters, code,
                null, companyId);
        dto.setConditionScreen(conditionScreen);
        //排序条件
        String buildSortNew = SqlUtils.buildSortNew(sortList, code,
                null, companyId);
        dto.setBuildSortNew(buildSortNew);
        //转换行转列值
        List<String> rowToColumnSql = SqlUtils.buildRowToColumnSqlNew("col_name",
                "col_value", code, null, companyId);
        dto.setRowToColumnSql(rowToColumnSql);
        return dto;
    }

    /**
     * 根据from表单查询拼接行专列的值
     * 组装排序条件
     * @param expoForm 表单
     * @param filters 筛选条件
     * @param sortList 排序条件
     */
    public FormSqlFieldDTO getFromSqlFieldAndSort(ExpoForm expoForm, List<CollectFilter> filters, List<CollectFieldSort> sortList){
        FormSqlFieldDTO dto = new FormSqlFieldDTO();
        //筛选条件
        String conditionScreen = SqlUtils.buildConditionNew(filters, null,
                expoForm.getFormCode(), expoForm.getCompanyId());
        dto.setConditionScreen(conditionScreen);
        //排序条件
        String buildSortNew = SqlUtils.buildSortNew(sortList, null,
                expoForm.getFormCode(), expoForm.getCompanyId());
        dto.setBuildSortNew(buildSortNew);
        //转换行转列值
        List<String> rowToColumnSql = SqlUtils.buildRowToColumnSqlNew("col_name",
                "col_value", null, expoForm.getFormCode(), expoForm.getCompanyId());
        dto.setRowToColumnSql(rowToColumnSql);
        return dto;
    }

}
