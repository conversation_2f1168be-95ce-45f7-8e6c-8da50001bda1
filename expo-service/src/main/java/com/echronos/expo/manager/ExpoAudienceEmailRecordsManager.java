/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoAudienceEmailRecordsDao;
import com.echronos.expo.model.ExpoAudienceEmailRecords;
import com.echronos.expo.model.ExpoExhibitorEmailRecords;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * ExpoAudienceEmailRecords Manager
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Component
public class ExpoAudienceEmailRecordsManager extends ServiceImpl<ExpoAudienceEmailRecordsDao, ExpoAudienceEmailRecords> {

    /**
     * 根据展商ID查询自动发送记录
     * @param audienceIds 观众ID
     * @param autoRuleIds 自动规则ID
     * @return
     */
    public List<ExpoAudienceEmailRecords> getAutoSendList(List<Integer> audienceIds, List<Integer> autoRuleIds){
        LambdaQueryWrapper<ExpoAudienceEmailRecords> queryWrapper = new LambdaQueryWrapper<ExpoAudienceEmailRecords>()
                .in(ExpoAudienceEmailRecords::getAudienceId, audienceIds)
                .in(ExpoAudienceEmailRecords::getAutoRuleId, autoRuleIds)
                .eq(ExpoAudienceEmailRecords::getIsAutoSend, CommonStatus.YesOrNoEnum.YES.getValue())
                .eq(ExpoAudienceEmailRecords::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

}
