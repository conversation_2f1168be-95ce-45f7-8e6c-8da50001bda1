/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoExhibitorScanRecordDao;
import com.echronos.expo.dto.ExpoExhibitorScanRecordDTO;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import com.echronos.expo.model.ext.ExpoExhibitorScanCountExt;
import com.echronos.expo.model.ext.ExpoExhibitorScanRecordExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * ExpoExhibitorScanRecord Manager
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class ExpoExhibitorScanRecordManager extends ServiceImpl<ExpoExhibitorScanRecordDao, ExpoExhibitorScanRecord> {

    @Resource
    private ExpoExhibitorScanRecordDao expoExhibitorScanRecordDao;

    /**
     * 查询展商扫码记录列表
     *
     * @param dto 查询参数
     * @return 展商扫码记录列表
     */
    public List<ExpoExhibitorScanRecord> queryList(ExpoExhibitorScanRecordDTO dto) {
        LambdaQueryWrapper<ExpoExhibitorScanRecord> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorScanRecord>()
                .eq(ExpoExhibitorScanRecord::getExpoId, dto.getExpoId())
                .eq(Objects.nonNull(dto.getExhibitorId()), ExpoExhibitorScanRecord::getExhibitorId, dto.getExhibitorId())
                .eq(ExpoExhibitorScanRecord::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.list(queryWrapper);
    }

    /**
     * 统计展商扫码次数
     *
     * @param expoId 展会ID
     * @return 扫码次数
     */
    public Integer getExhibitorScanRecordsCount(Integer expoId) {
        LambdaQueryWrapper<ExpoExhibitorScanRecord> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorScanRecord>()
                .eq(ExpoExhibitorScanRecord::getExpoId, expoId)
                .eq(ExpoExhibitorScanRecord::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.count(queryWrapper);
    }

    /**
     * 按日期统计展商扫码次数
     *
     * @param expoId    展会ID
     * @return 日期和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByDate(Integer expoId) {
        return expoExhibitorScanRecordDao.countScanRecordsByDate(expoId);
    }

    /**
     * 按小时统计展商扫码次数
     *
     * @param expoId         展会ID
     * @param statisticsDate 统计日期
     * @return 小时和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByHour(Integer expoId, LocalDate statisticsDate) {
        return expoExhibitorScanRecordDao.countScanRecordsByHour(expoId, statisticsDate);
    }

    /**
     * 按展商统计扫码次数
     *
     * @param expoId    展会ID
     * @return 展商ID和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByExhibitor(Integer expoId) {
        return expoExhibitorScanRecordDao.countScanRecordsByExhibitor(expoId);
    }

    /**
     * 分页查询展商扫码记录
     *
     * @param page 分页对象
     * @param dto 参数DTO
     * @return 展商扫码记录列表
     */
    public List<ExpoExhibitorScanRecordExt> pageExhibitorScanRecordList(Page<ExpoExhibitorScanRecordDTO> page, ExpoExhibitorScanRecordDTO dto){
        return expoExhibitorScanRecordDao.pageExhibitorScanRecordList(page, dto);
    }

    /**
     * 分页查询展商扫码观众
     *
     * @param page 分页对象
     * @param dto 参数DTO
     * @return 展商扫码观众列表
     */
    public List<ExpoExhibitorScanRecordDTO> pageExhibitorScanAudienceList(Page<ExpoExhibitorScanRecordDTO> page, ExpoExhibitorScanRecordDTO dto) {
        return expoExhibitorScanRecordDao.pageExhibitorScanAudienceList(page, dto);
    }

    /**
     * 统计展商扫码次数（报表）
     *
     * @param companyId 公司ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 扫码次数
     */
    public Integer getExhibitorScanRecordsCountTotal(Integer companyId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ExpoExhibitorScanRecord> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorScanRecord>()
                .eq(ExpoExhibitorScanRecord::getCompanyId, companyId)
                .between(ExpoExhibitorScanRecord::getCreateTime, startTime, endTime)
                .eq(ExpoExhibitorScanRecord::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.count(queryWrapper);
    }

}
