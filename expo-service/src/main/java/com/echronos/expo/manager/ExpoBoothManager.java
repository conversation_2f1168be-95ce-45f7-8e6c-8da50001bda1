/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoBoothDao;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoBoothPageDTO;
import com.echronos.expo.enums.ExpoBoothEnums;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ExpoBooth;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


/**
 * ExpoBooth Manager
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
public class ExpoBoothManager extends ServiceImpl<ExpoBoothDao, ExpoBooth> {

    @Resource
    private ExpoBoothDao expoBoothDao;

    /**
     * 展位统计
     * @param expoId 展位id
     * @return 结果
     */
    public ExpoBoothDTO boothStatistics(Integer expoId){
        return expoBoothDao.boothStatistics(expoId);
    }

    /**
     * 展位列表（分页）
     * @param page 分页参数
     * @param dto 查询参数
     * @return
     */
    public List<ExpoBoothDTO> pageList(Page<ExpoBoothDTO> page, ExpoBoothPageDTO dto){
        return expoBoothDao.pageList(page, dto);
    }

    /**
     * 根据ID查询
     * @param id ID
     * @return
     */
    public ExpoBooth getBoothById(Integer id){
        LambdaQueryWrapper<ExpoBooth> queryWrapper = new LambdaQueryWrapper<ExpoBooth>()
                .eq(ExpoBooth::getId, id)
                .eq(ExpoBooth::getIsDeleted, false);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ID查询
     * @param ids
     * @return
     */
    public List<ExpoBooth> getBoothByIds(List<Integer> ids){
        LambdaQueryWrapper<ExpoBooth> queryWrapper = new LambdaQueryWrapper<ExpoBooth>()
                .in(ExpoBooth::getId, ids)
                .eq(ExpoBooth::getIsDeleted, false);
        return this.list(queryWrapper);
    }

    /**
     * 根据展会ID查询展位
     * @param expoId 展会ID
     * @return
     */
    public List<ExpoBooth> getBoothByExpoId(Integer expoId){
        LambdaQueryWrapper<ExpoBooth> queryWrapper = new LambdaQueryWrapper<ExpoBooth>()
                .eq(ExpoBooth::getExpoId, expoId)
                .eq(ExpoBooth::getIsDeleted, false);
        return this.list(queryWrapper);
    }

    /**
     * 检查展位是否重复
     *
     * @param expoId 展会ID
     * @param boothName 展馆号/名称
     * @param boothFloor 楼层
     * @param boothZone 区号/区域
     * @param boothNumber 展位号
     * @param excludeId 排除的展位ID（编辑时使用，新增时为null）
     * @return true-重复 false-不重复
     */
    public boolean isBoothDuplicate(Integer expoId, String boothName, String boothFloor, String boothZone, String boothNumber, Integer excludeId) {
        long count = this.lambdaQuery()
                .eq(ExpoBooth::getExpoId, expoId)
                .eq(ExpoBooth::getBoothName, boothName)
                .eq(ExpoBooth::getBoothFloor, boothFloor)
                .eq(ExpoBooth::getBoothZone, boothZone)
                .eq(ExpoBooth::getBoothNumber, boothNumber)
                .eq(ExpoBooth::getIsDeleted, false)
                .ne(excludeId != null, ExpoBooth::getId, excludeId)
                .count();
        return count > 0;
    }

    /**
     * 批量更新展位状态
     * @param ids  展位ID
     * @param status 状态
     * @param updateUser 更新人
     * @param updateTime 更新时间
     */
    public void updateStatus(List<Integer> ids, Integer status, Integer updateUser, LocalDateTime updateTime) {
        LambdaUpdateWrapper<ExpoBooth> updateWrapper = new LambdaUpdateWrapper<ExpoBooth>()
                .in(ExpoBooth::getId, ids)
                .set(ExpoBooth::getStatus, status)
                .set(ExpoBooth::getUpdateUser, updateUser)
                .set(ExpoBooth::getUpdateTime, updateTime)
                ;
        this.update(updateWrapper);
    }

    /**
     * 根据时间筛选出已售展位
     * @param expoId
     * @param startTime
     * @param endTime
     */
    public List<ExpoBooth> queryBoothByCreateTime(Integer expoId, LocalDateTime startTime, LocalDateTime endTime){
        LambdaQueryWrapper<ExpoBooth> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoBooth::getExpoId, expoId)
                .eq(ExpoBooth::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoBooth::getStatus, ExpoBoothEnums.Status.SOLD.getCode())
                .between(ExpoBooth::getCreateTime, startTime, endTime);
        return list(lambdaQueryWrapper);
    }

    /**
     * 获取首页展位统计
     * @param companyId 公司id
     * @return 结果
     */
    public ExpoIndexCountExt getIndexBoothCount(Integer companyId){
        return expoBoothDao.getIndexBoothCount(companyId);
    }
}
