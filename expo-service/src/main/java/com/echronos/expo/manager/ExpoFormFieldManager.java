/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoFormFieldDao;
import com.echronos.expo.dto.ExpoFormFieldDTO;
import com.echronos.expo.model.ExpoFormField;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * EchExpoFormComponent Manager
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class ExpoFormFieldManager extends ServiceImpl<ExpoFormFieldDao, ExpoFormField> {

    public ExpoFormField queryOne(ExpoFormFieldDTO dto) {
        LambdaQueryWrapper<ExpoFormField> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoFormField::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getId(), ExpoFormField::getId, dto.getId())
                .eq(null != dto.getExpoId(), ExpoFormField::getExpoId, dto.getExpoId());
        return getOne(queryWrapper);
    }

    public void delById(Integer id) {
        LambdaUpdateWrapper<ExpoFormField> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpoFormField::getExpoId, id)
                .eq(ExpoFormField::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoFormField::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        this.update(updateWrapper);
    }

    public List<ExpoFormField> queryList(ExpoFormFieldDTO dto) {
        LambdaQueryWrapper<ExpoFormField> queryWrapper = new LambdaQueryWrapper<ExpoFormField>()
                .eq(ExpoFormField::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getExpoId(), ExpoFormField::getExpoId, dto.getExpoId());
        return this.list(queryWrapper);
    }
}
