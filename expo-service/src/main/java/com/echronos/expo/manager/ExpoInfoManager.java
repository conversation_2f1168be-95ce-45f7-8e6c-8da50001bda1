/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoInfoPageDTO;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import com.echronos.expo.model.ext.ExpoInfoExt;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoInfoDao;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ExpoInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import java.util.List;


/**
 * EchExpoInfo Manager
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class ExpoInfoManager extends ServiceImpl<ExpoInfoDao, ExpoInfo> {

    @Resource
    private ExpoInfoDao expoInfoDao;

    /**
     * 分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return
     */
    public Page<ExpoInfo> getExpoInfoByPage(int pageNum, int pageSize) {
        LambdaQueryWrapper<ExpoInfo> queryWrapper = new LambdaQueryWrapper<ExpoInfo>()
                .eq(BaseEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return page(new Page<>(pageNum, pageSize), queryWrapper);
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ExpoInfo getById(Integer id){
        LambdaQueryWrapper<ExpoInfo> queryWrapper = new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getId, id)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据公司id查询展会列表
     * @param companyId 公司ID
     * @return
     */
    public List<ExpoInfo> getListByCompanyId(Integer companyId){
        LambdaQueryWrapper<ExpoInfo> queryWrapper = new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getCompanyId, companyId)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 分页查询展会信息
     * @param companyId
     * @return
     */
    public List<ExpoInfo> queryWebExpoInfo(Integer companyId){
        LambdaQueryWrapper<ExpoInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoInfo::getCompanyId, companyId)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .orderByDesc(ExpoInfo::getCreateTime);
        return list(lambdaQueryWrapper);
    }

    /**
     * 分页查询展会信息
     * @param page
     * @param dto
     * @return
     */
    public List<ExpoInfoExt> pageList(Page page, ExpoInfoPageDTO dto){
        return expoInfoDao.pageList(page, dto);
    }

    /**
     * 分页查询展会信息（观众端）
     * @param page
     * @param dto
     * @return
     */
    public List<ExpoInfoExt> pageListAudience(Page page, ExpoInfoPageDTO dto){
        return expoInfoDao.pageListAudience(page, dto);
    }



    /**
     * 获取展会总数（首页）
     * @param companyId
     * @return
     */
    public ExpoIndexCountExt getExpoIndexCount(Integer companyId){
        return expoInfoDao.getExpoIndexCount(companyId);
    }

    /**
     * 根据ID查询展会
     * @param id
     * @return
     */
    public ExpoInfo getOneById(Integer id) {
        return this.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getId, id)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
        );
    }



}
