/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.model.BaseNotTenantEntity;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoReferenceFormExtend;
import com.echronos.expo.dao.ExpoReferenceFormExtendDao;

import java.util.List;


/**
 * ExpoReferenceFormExtend Manager
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Component
public class ExpoReferenceFormExtendManager extends ServiceImpl<ExpoReferenceFormExtendDao, ExpoReferenceFormExtend> {

    /**
     * 根据表单ID查询值
     * @param referenceFormId 表单ID
     * @return
     */
    public List<ExpoReferenceFormExtend> getListByRefFormId(Integer referenceFormId) {
        LambdaQueryWrapper<ExpoReferenceFormExtend> queryWrapper = new LambdaQueryWrapper<ExpoReferenceFormExtend>()
                .eq(ExpoReferenceFormExtend::getReferenceFormId, referenceFormId)
                .eq(ExpoReferenceFormExtend::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 根据表单ID列表查询值
     * @param referenceFormIds 表单ID列表
     * @return
     */
    public List<ExpoReferenceFormExtend> getListByRefFormIds(List<Integer> referenceFormIds){
        LambdaQueryWrapper<ExpoReferenceFormExtend> queryWrapper = new LambdaQueryWrapper<ExpoReferenceFormExtend>()
                .in(ExpoReferenceFormExtend::getReferenceFormId, referenceFormIds)
                .eq(ExpoReferenceFormExtend::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 根据表单ID删除
     * @param referenceFormId
     */
    public void removeByRefFormId(Integer referenceFormId) {
        LambdaQueryWrapper<ExpoReferenceFormExtend> queryWrapper = new LambdaQueryWrapper<ExpoReferenceFormExtend>()
                .eq(ExpoReferenceFormExtend::getReferenceFormId, referenceFormId)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        remove(queryWrapper);
    }

}
