/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoAudienceDao;
import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.dto.ExpoAudiencePageDTO;
import com.echronos.expo.model.ExpoAudience;
import com.echronos.expo.model.ext.ExpoAudienceDateCountExt;
import com.echronos.expo.model.ext.ExpoAudienceExt;
import com.echronos.expo.model.ext.ExpoChannelAudienceExt;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * EchExpoAudience Manager
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Component
public class ExpoAudienceManager extends ServiceImpl<ExpoAudienceDao, ExpoAudience> {

    @Resource
    private ExpoAudienceDao expoAudienceDao;

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ExpoAudience queryById(Integer id){
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getId, id)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return
     */
    public Page<ExpoAudience> getExpoAudienceByPage(int pageNum, int pageSize, Integer expoId) {
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return page(new Page<>(pageNum, pageSize), queryWrapper);
    }

    /**
     * 分页查询观众
     *
     * @param page
     * @param dto
     * @return
     */
    public Page<ExpoAudienceDTO> pageForAudience(Page<ExpoAudienceDTO> page, ExpoAudiencePageDTO dto) {
        return expoAudienceDao.pageForAudience(page, dto);
    }

    /**
     * 分页查询观众列表
     * @param page
     * @param dto
     * @return
     */
    public Page<ExpoAudienceDTO> pageList(Page<ExpoAudienceDTO> page, ExpoAudiencePageDTO dto){
        return expoAudienceDao.pageList(page, dto);
    }

    /**
     * 根据邮箱查询
     * @param emails
     * @param expoId
     * @param companyId
     * @return
     */
    public List<ExpoAudience> listByEmails(List<String> emails, Integer expoId, Integer companyId){
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .in(CollectionUtils.isNotEmpty(emails), ExpoAudience::getEmail, emails)
                .eq(ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getCompanyId, companyId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 查询观众
     * @param dto
     * @return
     */
    public List<ExpoAudienceDTO> listAudience(ExpoAudiencePageDTO dto) {
        return expoAudienceDao.pageForAudience(dto);
    }

    /**
     * 根据观众ID查询观众信息
     * @param id
     * @param expoId
     * @return
     */
    public ExpoAudience queryAudienceById(Integer id, Integer expoId) {
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getId, id)
                .eq(Objects.nonNull(expoId), ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 根据展会ID和邮箱查询观众信息
     * @param expoId
     * @param email
     * @return
     */
    public ExpoAudience queryExistAudience(Integer expoId, String email){
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getEmail, email)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return getOne(lambdaQueryWrapper);
    }


    /**
     * 根据公司ID查询观众总数量
     * @param companyId
     * @param startTime
     * @param endTime
     * @return
     */
    public Integer queryAllAudienceCount(Integer companyId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getCompanyId, companyId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .between(ExpoAudience::getCreateTime, startTime, endTime);
        return count(lambdaQueryWrapper);
    }

    /**
     * 查询观众到场次数
     * @param dto
     * @return
     */
    public List<Integer> queryAudienceIdList(ExpoAudienceDTO dto){
        return expoAudienceDao.queryAudienceIdList(dto);
    }


    /**
     * 获取首页观众数量
     * @param companyId
     * @return
     */
    public ExpoIndexCountExt getIndexAudienceCount(Integer companyId){
        return expoAudienceDao.getIndexAudienceCount(companyId);
    }

    /**
     * 获取展会观众数量
     * @param expoId 展会ID
     * @return
     */
    public Integer getAudienceCount(Integer expoId) {
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.count(queryWrapper);
    }

    /**
     * 获取展会观众签到数量
     * @param expoId
     * @return
     */
    public Integer getAudienceSignInCount(Integer expoId) {
        return expoAudienceDao.getAudienceSignInCount(expoId);
    }

    /**
     * 获取展会累计签到数量
     * @param expoId
     * @return
     */
    public Integer getAudienceCumulativeSignInCount(Integer expoId) {
        return expoAudienceDao.getAudienceCumulativeSignInCount(expoId);
    }

    /**
     * 获取展会渠道观众数量
     * @param expoId
     * @return
     */
    public List<ExpoChannelAudienceExt> getChannelAudienceCountList(Integer expoId){
        return expoAudienceDao.getChannelAudienceCountList(expoId);
    }

    /**
     * 获取展会观众每天签到数量
     * @param expoId
     * @return
     */
    public List<ExpoAudienceDateCountExt> getAudienceSingInDayCount(Integer expoId){
        return expoAudienceDao.getAudienceSingInDayCount(expoId);
    }

    /**
     * 获取展会观众签到时间段统计
     * @param expoId
     * @param time
     * @return
     */
    public List<ExpoAudienceDateCountExt> getAudienceSingInTimeCount(Integer expoId, LocalDateTime time){
        return expoAudienceDao.getAudienceSingInTimeCount(expoId, time);
    }



    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ExpoAudience getOneById(Integer id){
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getId,  id)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 查询观众
     *
     * @param dto
     * @return
     */
    public ExpoAudience queryBy(ExpoAudienceDTO dto) {
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoAudience::getExpoId, dto.getExpoId())
                .eq(ExpoAudience::getCustomerId, dto.getCustomerId());
        return this.getOne(queryWrapper);
    }

    /**
     * 批量根据ID查询
     *
     * @param ids
     * @return
     */
    public List<ExpoAudience> queryByIds(List<Integer> ids) {
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .in(ExpoAudience::getId,  ids)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 分页查询打印签到观众列表
     * @param dto
     * @param page
     * @return
     */
    public List<ExpoAudienceDTO> printSignPageList(ExpoAudiencePageDTO dto, Page<ExpoAudience> page){
        return expoAudienceDao.printSignPageList(dto, page);
    }

    /**
     * 更新打印签到次数
     * @param id
     * @param qrCodePrintCount
     */
    public void updateQrCodePrintSignCount(Integer id, Integer qrCodePrintCount){
        LambdaUpdateWrapper<ExpoAudience> updateWrapper = new LambdaUpdateWrapper<ExpoAudience>()
                .eq(ExpoAudience::getId, id)
                .set(ExpoAudience::getQrCodePrintCount, qrCodePrintCount);
        update(updateWrapper);
    }

    /**
     * 分页查询观众详情
     * @param dto
     * @param
     * @return
     */
    public Page<ExpoAudience> pageAudienceDetail(ExpoAudienceDTO dto) {
        Page<ExpoAudience> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        LambdaQueryWrapper<ExpoAudience> queryWrapper = new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .between(ExpoAudience::getCreateTime, dto.getStartTime(), dto.getEndTime());
        return this.page(page, queryWrapper);
    }

    /**
     * 查询观众列表
     * @param dto
     * @param
     * @return
     */
    public List<ExpoAudienceExt> querylist(ExpoAudienceDTO dto) {
        return expoAudienceDao.queryList(dto);
    }
}
