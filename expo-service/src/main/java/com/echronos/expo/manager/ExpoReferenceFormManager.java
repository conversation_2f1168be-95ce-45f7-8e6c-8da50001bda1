/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoReferenceFormDTO;
import com.echronos.expo.model.BaseNotTenantEntity;
import com.echronos.expo.model.ExpoReferenceFormExtend;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoReferenceForm;
import com.echronos.expo.dao.ExpoReferenceFormDao;

import javax.annotation.Resource;
import java.util.List;


/**
 * ExpoReferenceForm Manager
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Component
public class ExpoReferenceFormManager extends ServiceImpl<ExpoReferenceFormDao, ExpoReferenceForm> {

    @Resource
    private ExpoReferenceFormDao expoReferenceFormDao;

    /**
     * 根据业务ID查询表单
     * @param expoId 展会ID
     * @param businessId 业务ID
     * @param formGroup 表单组
     * @return
     */
    public List<ExpoReferenceForm> getListByBusinessIdFromGroup(Integer expoId, Integer businessId, Integer formGroup) {
        LambdaQueryWrapper<ExpoReferenceForm> queryWrapper = new LambdaQueryWrapper<ExpoReferenceForm>()
                .eq(ExpoReferenceForm::getExpoId, expoId)
                .eq(ExpoReferenceForm::getBusinessId, businessId)
                .eq(ExpoReferenceForm::getFormGroup, formGroup)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 根据业务ID查询表单
     * @param businessIdList
     * @param formGroup
     * @param formType
     * @return
     */
    public List<ExpoReferenceForm> getListByBusinessIdAndType(List<Integer> businessIdList, Integer formGroup, Integer formType) {
        LambdaQueryWrapper<ExpoReferenceForm> queryWrapper = new LambdaQueryWrapper<ExpoReferenceForm>()
                .eq(ExpoReferenceForm::getBusinessId, businessIdList)
                .eq(ExpoReferenceForm::getFormType, formType)
                .eq(ExpoReferenceForm::getFormGroup, formGroup)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }


    /**
     * 根据ID查询表单
     * @param id 表单ID
     * @return
     */
    public ExpoReferenceForm getById(Integer id) {
        LambdaQueryWrapper<ExpoReferenceForm> queryWrapper = new LambdaQueryWrapper<ExpoReferenceForm>()
                .eq(ExpoReferenceForm::getId, id)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据表单组、表单类型、业务ID查询表单
     * @param business 业务ID
     * @param formGroup 表单组
     * @param formType 表单类型
     * @return
     */
    public ExpoReferenceForm getByGroupTypeBusinessId(Integer business, Integer formGroup, Integer formType){
        ExpoReferenceFormDTO dto = new ExpoReferenceFormDTO();
        dto.setBusinessId(business);
        dto.setFormGroup(formGroup);
        dto.setFormType(formType);
        return expoReferenceFormDao.getByGroupTypeBusinessId(dto);
    }

    /**
     * 删除表单
     * @param business
     * @param formGroup
     * @param formType
     */
    public void remove(Integer business, Integer formGroup, Integer formType){
        LambdaUpdateWrapper<ExpoReferenceForm> updateWrapper = new LambdaUpdateWrapper<ExpoReferenceForm>()
                .eq(ExpoReferenceForm::getBusinessId, business)
                .eq(ExpoReferenceForm::getFormType, formType)
                .eq(ExpoReferenceForm::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoReferenceForm::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        remove(updateWrapper);
    }

}
