package com.echronos.expo.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.echronos.bidding.feign.IBiddingTrendsFeignClient;
import com.echronos.bidding.req.IdReq;
import com.echronos.bidding.resp.BiddingTrendsResp;
import com.echronos.commons.Result;
import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.exception.ParamsValidateException;
import com.echronos.crm.enums.CustomerTypeEnum;
import com.echronos.crm.feign.*;
import com.echronos.crm.req.*;
import com.echronos.crm.resp.CategoryResp;
import com.echronos.crm.resp.ContactResp;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.crm.resp.SupplierIsExistResp;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.iform.api.feign.IComponentAttrFeign;
import com.echronos.iform.api.feign.IFormFeign;
import com.echronos.iform.api.req.ComponentReq;
import com.echronos.iform.api.req.CopyFormReq;
import com.echronos.iform.api.req.FormCodeReq;
import com.echronos.iform.api.resp.ComponentResp;
import com.echronos.iform.api.resp.FormResp;
import com.echronos.imc.api.feign.IImcClient;
import com.echronos.imc.api.feign.IImcServiceClient;
import com.echronos.imc.api.req.GroupCreateReq;
import com.echronos.imc.api.req.QueryServiceReq;
import com.echronos.imc.api.resp.CscCustomerServiceResp;
import com.echronos.imc.api.resp.ImcSessionResp;
import com.echronos.mcs.api.feign.IShopSkuFeign;
import com.echronos.mcs.api.req.BaseIdsReq;
import com.echronos.mcs.api.resp.ShopSkuBasisResp;
import com.echronos.nms.api.enums.NmsMsgTypeEnums;
import com.echronos.nms.api.enums.NmsTemplateEnums;
import com.echronos.nms.api.feign.IEMailFeign;
import com.echronos.nms.api.feign.IIMSendMsgFeign;
import com.echronos.nms.api.req.IMMessageReq;
import com.echronos.nms.api.req.QueryEmailSmtpReq;
import com.echronos.nms.api.req.QueryEmailTempReq;
import com.echronos.nms.api.resp.EmailSmtpConfigInfoResp;
import com.echronos.nms.api.resp.EmailTemplateInfoResp;
import com.echronos.order.feign.IOrderFeign;
import com.echronos.order.feign.IPayWayFeign;
import com.echronos.order.req.DefaultPayWayReq;
import com.echronos.order.req.GenOrderParentReq;
import com.echronos.order.req.OrderPaymentInfoReq;
import com.echronos.order.resp.GenOrderResp;
import com.echronos.order.resp.OrderPaymentInfoResp;
import com.echronos.order.resp.PayWayResp;
import com.echronos.pms.enums.TypeEnum;
import com.echronos.pms.feign.IProductFeign;
import com.echronos.pms.feign.ISkuFeign;
import com.echronos.pms.req.AddSkuReq;
import com.echronos.pms.req.BatchAddSkuReq;
import com.echronos.pms.req.QuerySkuReq;
import com.echronos.pms.resp.AddSkuResp;
import com.echronos.pms.resp.SkuListResp;
import com.echronos.search.api.feign.ProductSearchClient;
import com.echronos.search.api.req.PmsProductReq;
import com.echronos.search.api.resp.PmsProductResp;
import com.echronos.system.feign.IMemberFeign;
import com.echronos.system.feign.IPermissionFeign;
import com.echronos.system.req.QueryMemberByIdReq;
import com.echronos.system.req.permission.HavePermListReq;
import com.echronos.system.resp.MemberResp;
import com.echronos.system.resp.member.MemberPowerResp;
import com.echronos.system.resp.member.MemberSimpleResp;
import com.echronos.tenant.api.feign.TenantInfoFeignClient;
import com.echronos.tenant.api.req.BatchTenantDetailReq;
import com.echronos.tenant.api.req.CompanyIdReq;
import com.echronos.tenant.api.req.TenantInfoReq;
import com.echronos.tenant.api.resp.TenantInfoResp;
import com.echronos.user.api.feign.CompanyResourceFeign;
import com.echronos.user.api.feign.IUserService;
import com.echronos.user.api.feign.MemberResourceFeign;
import com.echronos.user.api.req.BatchUserReq;
import com.echronos.user.api.req.QueryCompanyIdReq;
import com.echronos.user.api.req.QueryUserReq;
import com.echronos.user.api.resp.UserInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.echronos.user.api.resp.member.BatchMemberInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-01 19:06
 */
@Slf4j
@Service
public class FeignCommonManager {

    @Resource
    private ICustomerClient customerClient;
    @Resource
    private ISkuFeign skuFeign;
    @Resource
    private IProductFeign productFeign;
    @Resource
    private IMemberFeign memberFeign;
    @Resource
    private IUserService iUserService;
    @Resource
    private CompanyResourceFeign companyResourceFeign;
    @Resource
    private IOrderFeign orderFeign;
    @Resource
    private IPayWayFeign payWayFeign;
    @Resource
    private IUserService userService;
    @Resource
    private MemberResourceFeign memberResourceFeign;
    @Resource
    private ProductSearchClient productSearchClient;
    @Resource
    private IShopSkuFeign shopSkuFeign;
    @Resource
    private IFormFeign formFeign;
    @Resource
    private TenantInfoFeignClient tenantInfoFeignClient;
    @Resource
    private IImcClient imcClient;
    @Resource
    private IPermissionFeign permissionFeign;
    @Resource
    private IImcServiceClient imcServiceClient;
    @Resource
    private ICategoryClient iCategoryClient;
    @Resource
    private IComponentAttrFeign iComponentAttrFeign;
    @Resource
    private IIMSendMsgFeign imSendMsgFeign;
    @Resource
    private IBusinessCardExchangeClient iBusinessCardExchangeClient;
    @Resource
    private ISupplierClient iSupplierClient;
    @Resource
    private IBiddingTrendsFeignClient iBiddingTrendsFeignClient;
    @Resource
    private IComponentAttrFeign componentAttrFeign;
    @Resource
    private IContactClient contactClient;
    @Resource
    private IEMailFeign iEMailFeign;

    /**
     * 批量查询客户信息
     *
     * @param customerIdList 客户ID列表
     * @return
     */
    public Map<Integer, CustomerResp> getBatchCustomerByIds(List<Integer> customerIdList) {
        BatchCustomerIdReq req = new BatchCustomerIdReq();
        req.setCustomerIdList(customerIdList);
        req.setCustomerType(CustomerTypeEnum.CUSTOMER.getCode());
        Result<List<CustomerResp>> result = customerClient.queryCustomerListByCustomerIdList(req);
        if (!result.isSuccess()) {
            log.error("调用【ICustomerClient.queryCustomerListByCustomerIdList】查询客户异常,req={},error={}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData().stream().collect(Collectors.toMap(CustomerResp::getId, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 获取客户信息
     *
     * @param companyId 公司ID
     * @param phone     手机号
     * @param email     邮箱
     * @return
     */
    public CustomerResp getIndividualPhoneOrEmail(Integer companyId, String phone, String email){
        CustomerGetPhoneOrEmailReq req = new CustomerGetPhoneOrEmailReq();
        req.setCompanyId(companyId);
        req.setPhone(phone);
        req.setEmail(email);
        Result<CustomerResp> result = customerClient.getIndividualPhoneOrEmail(req);
        if(!result.isSuccess()){
            log.error("getIndividualPhoneOrEmail->customerClient.getIndividualPhoneOrEmail() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
        }
        return result.getData();
    }


    /**
     * 生成商品
     *
     * @param userId     用户ID
     * @param companyId  公司ID
     * @param addSkuList 需生成的商品信息
     * @return 商品及对应的数据ID
     */
    public Map<Integer, AddSkuResp> batchInitSku(Integer userId, Integer companyId, List<AddSkuReq> addSkuList) {
        BatchAddSkuReq req = new BatchAddSkuReq();
        req.setUserId(userId);
        req.setCompanyId(companyId);
        req.setSource(TypeEnum.SourceTypeEnum.INQUIRY_QUOTATION_PRODUCT.getCode());
        req.setAddSkuList(addSkuList);
        Result<List<AddSkuResp>> result = skuFeign.batchAddSku(req);
        if (!result.isSuccess()) {
            log.error("batchInitSku->skuFeign.batchAddSku() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_INIT_SKU_ERROR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_INIT_SKU_ERROR.getMessage());
        }
        Map<Integer, AddSkuResp> map = result.getData().stream().collect(Collectors.toMap(AddSkuResp::getBusinessId, Function.identity(), (key1, key2) -> key2));
        return map;
    }


    /**
     * 批量根据skuId查询商品基本信息
     *
     * @param skuIdList skuId集合
     * @return 商品基本信息Map结构
     */
    public Map<Integer, SkuListResp> querySkuByIdsToMap(List<Integer> skuIdList) {
        QuerySkuReq querySkuReq = new QuerySkuReq();
        querySkuReq.setList(skuIdList);
        Map<Integer, SkuListResp> skuMap = productFeign.findSkuByIds(querySkuReq);
        return skuMap;
    }

    /**
     * 根据会员ID查询会员信息
     * @param memberId 会员ID
     * @return 会员信息
     */
    public MemberResp getByMemberId(Integer memberId){
        QueryMemberByIdReq req = new QueryMemberByIdReq();
        req.setId(memberId);
        Result<MemberResp> result = memberFeign.getMemberById(req);
        if(!result.isSuccess()){
            log.error("getByMemberId->memberFeign.getMemberById() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
        }
        return result.getData();
    }


    /**
     * 生成客户信息
     *
     * @param req
     */
    public void insertCustomer(InsertCustomerReq req) {
        Result<Boolean> result = customerClient.insertCustomer(req);
        if (!result.isSuccess()) {
            log.error("调用【ICustomerClient.insertCustomer】生成客户信息异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
        }
    }

    /**
     * 根据e-mail或者phone 查询成员信息
     *
     * @param phone
     * @param name
     * @param email
     * @return
     */
    public MemberPowerResp getProprietorshipId(String phone, String name, String email) {
        QueryCompanyIdReq req = new QueryCompanyIdReq();
        req.setPhone(phone);
        req.setCustomerName(name);
        req.setEmail(email);
        req.setDataOrigin(0);
        log.info("查询ech-user个人公司信息req:{}", JSONObject.toJSONString(req));
        Result<MemberPowerResp> result = iUserService.getProprietorshipId(req);
        if (!result.isSuccess()) {
            log.error("根据e-mail查询成员信息【user-api】异常, req = {}, error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量根据公司ID查询公司信息
     * @param companyIdList
     * @return
     */
    public Map<Integer, QueryCompanyResp> queryCompanyByIds(List<Integer> companyIdList){
        Result<Map<Integer, QueryCompanyResp>> result = companyResourceFeign.batchQueryCompanyByIds(companyIdList);
        if (!result.isSuccess()) {
            log.error("批量根据公司ID查询信息【user-api】异常, req = {}, error = {}", JSON.toJSONString(companyIdList), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取服务订单付款方式
     * @param companyId 公司ID
     * @return 付款方式
     */
    public PayWayResp getExpenseOrderPayWay(Integer companyId){
        DefaultPayWayReq req = new DefaultPayWayReq();
        req.setCompanyId(companyId);
        Result<PayWayResp> result = payWayFeign.getExpenseOrderPayWay(req);
        if(!result.isSuccess()){
            log.error("调用【IPayWayFeign.getExpenseOrderPayWay】查询服务订单付款方式异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量生成订单
     * @param req
     * @return
     */
    public Map<Integer, GenOrderResp> addGenOrder(GenOrderParentReq req) {
        Result<Map<Integer, GenOrderResp>> result = orderFeign.genOrder(req);
        if (!result.isSuccess()) {
            log.error("调用【IOrderFeign.genOrder】调用泛用性生成订单接口异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
            throw new ParamsValidateException(ExpoResultCode.ExpoResultEnum.EXPO_ORDER_CREATE_ERROR.getCode(), ExpoResultCode.ExpoResultEnum.EXPO_ORDER_CREATE_ERROR.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量查询订单付款信息
     * @param orderNoList
     * @return
     */
    public Map<Long, OrderPaymentInfoResp> batchQueryOrderPaymentInfo(List<Long> orderNoList){
        OrderPaymentInfoReq req = new OrderPaymentInfoReq();
        req.setOrderNoList(orderNoList);
        Result<List<OrderPaymentInfoResp>> result = orderFeign.batchQueryOrderPaymentInfo(req);
        if(!result.isSuccess()){
            log.error("调用【IOrderFeign.batchQueryOrderPaymentInfo】批量查询订单付款信息异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
            return null;
        }
        return CollectionUtils.isEmpty(result.getData()) ? new HashMap<>() : result.getData().stream().collect(Collectors.toMap(OrderPaymentInfoResp::getOrderNo, Function.identity()));
    }


    /**
     * 根据用户ID查询用户信息
     * @param req
     * @return
     */
    public UserInfoResp queryUserInfo(QueryUserReq req){
        Result<UserInfoResp> result = userService.getUserById(req);
        if(!result.isSuccess()){
            log.error("根据用户ID查询用户信息【user-api】异常, req ={}, error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }


    /**
     * 根据成员ID查询成员(批量)
     *
     * @param memberIds
     * @return
     */
    public Map<Integer, BatchMemberInfoResp> queryMemberDetailsByIds(List<Integer> memberIds) {
        Result<List<BatchMemberInfoResp>> result = memberResourceFeign.queryMemberDetailsByIds(memberIds);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用user服务<根据ID查询成员信息>异常。参数：req={}  error={}", memberIds, result.getMessage());
        }
        Map<Integer, BatchMemberInfoResp> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(result.getData())) {
            map = result.getData().stream().collect(Collectors.toMap(BatchMemberInfoResp::getId, Function.identity(), (key1, key2) -> key2));
        }
        return map;
    }


    /**
     * 调取搜索服务查询商品信息
     * @param pmsProductReq
     * @return
     */
    public Result<List<PmsProductResp>> searchProduct(PmsProductReq pmsProductReq){
        try{
            Result<List<PmsProductResp>> pmsProduct = productSearchClient.findPmsProduct(pmsProductReq);
            if(pmsProduct.getCode() == 0){
                return pmsProduct;
            }
        }catch (Exception e){
            log.error("调取搜索服务查询商品异常,异常信息:{}",e);
        }
        return Result.build();
    }

    /**
     * 批量根据公司ID查询公司信息
     *
     * @param companyId
     * @return
     */
    public QueryCompanyResp selectCompanyById(Integer companyId){
        Result<QueryCompanyResp> result = companyResourceFeign.selectCompanyById(companyId);
        if (!result.isSuccess()) {
            log.error("批量根据公司ID查询信息【user-api】异常, req = {}, error = {}", companyId, result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量根据店铺商品ID查询商品基础信息
     * @param shopSkuIds 店铺商品ID
     * @return 商品信息
     */
    public Map<Integer, ShopSkuBasisResp> queryShopSkuBasisByIds(Set<Integer> shopSkuIds){
        BaseIdsReq req = new BaseIdsReq();
        req.setIds(shopSkuIds);
        Result<Map<Integer, ShopSkuBasisResp>> result = shopSkuFeign.queryShopSkuBasisByIds(req);
        if(!result.isSuccess()){
            log.error("queryShopSkuBasisByIds->shopSkuFeign.queryShopSkuBasisByIds() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode(), CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getMessage());
        }
        return result.getData();
    }

    /**
     * 复制表单
     * @return
     */
    public Map<String, String> copyForm(Integer companyId, List<String> formCodes, Integer userId){
        CopyFormReq req = new CopyFormReq();
        req.setCompanyId(companyId);
        req.setFormCodes(formCodes);
        req.setCreateUser(userId);
        Result<Map<String, String>> result = formFeign.copyForm(req);
        if(!result.isSuccess()){
            log.error("copyForm->formFeign.copyForm() 复制创建表单调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode(), CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取表单属性
     * @param companyId
     * @param formCode
     * @return
     */
    public List<ComponentResp> getFormComponents(Integer companyId, String formCode){
        ComponentReq req = new ComponentReq();
        req.setCompanyId(companyId);
        req.setFormCode(formCode);
        return componentAttrFeign.getComponents(req);
    }

    /**
     * 获取表单信息
     * @param companyId
     * @param fromCode
     * @return
     */
    public FormResp getForm(Integer companyId, String fromCode){
        FormCodeReq req = new FormCodeReq();
        req.setCompanyId(companyId);
        req.setFromCode(fromCode);
        FormResp form = formFeign.getForm(req);
        return form;
    }

    /**
     * 根据租户ID获取租户信息
     *
     * @param tenantId
     * @return
     */
    public TenantInfoResp getTenantInfo(String tenantId) {
        try {
            TenantInfoReq tenantInfoReq = new TenantInfoReq();
            tenantInfoReq.setTenantId(tenantId);
            Result<TenantInfoResp> result = tenantInfoFeignClient.queryTenantInfo(tenantInfoReq);
            if (!result.isSuccess()) {
                log.error("<Feign> 调用tenant服务<根据租户ID或域名查询租户信息>异常，error：{}", result.getMessage());
            } else if (Objects.isNull(result.getData())) {
                log.info("未能根据租户ID或域名查询租户信息，req={}", tenantId);
            }
            return result.getData();
        } catch (Exception e) {
            log.error("tenantInfoFeignClient->queryTenantInfo(): " + e.getMessage());
        }
        return null;
    }

    /**
     * 创建群聊
     *
     * @param userIds
     * @param createUserId
     * @return
     */
    public ImcSessionResp createGroup(List<Integer> userIds, Integer createUserId){
        GroupCreateReq req = new GroupCreateReq();
        req.setUserIds(userIds);
        req.setCreateUserId(createUserId);
        Result<ImcSessionResp> result = imcClient.createGroup(req);
        if (!result.isSuccess()) {
            log.error("创建群聊【imc-api】异常, req = {}, error = {}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据公司Id和权限编码模糊查询拥有权限所有成员信息
     *
     * @param companyIds
     * @param permCode
     * @return
     */
    public Map<Integer, List<MemberSimpleResp>> queryMemberListBy(List<Integer> companyIds, String permCode){
        HavePermListReq req = new HavePermListReq();
        req.setCompanyIds(companyIds);
        req.setPermCode(permCode);
        Result<Map<Integer, List<MemberSimpleResp>>> result = permissionFeign.queryMemberListBy(req);
        if (!result.isSuccess()) {
            log.error("根据公司Id和权限编码模糊查询拥有权限所有成员信息【system-api】异常, req = {}, error = {}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据公司Id查询公司客服
     *
     * @param companyIds
     * @return
     */
    public List<CscCustomerServiceResp> queryCompanyService(List<Integer> companyIds){
        QueryServiceReq req = new QueryServiceReq();
        req.setCompanyIds(companyIds);
        Result<List<CscCustomerServiceResp>> result = imcServiceClient.queryCompanyService(req);
        if (!result.isSuccess()) {
            log.error("根据公司Id查询公司客服【imc-api】异常, req = {}, error = {}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据供应商ID查询分类信息
     *
     * @param supplierIds 供应商ID列表
     * @return 分类信息
     */
    public Map<Integer, List<CategoryResp>> queryCategoryListBySupplierId(List<Integer> supplierIds){
        SupplierCategoryReq req = new SupplierCategoryReq();
        req.setSupplierIdList(supplierIds);
        Result<Map<Integer, List<CategoryResp>>> result = iCategoryClient.queryCategoryListBySupplierId(req);
        if (!result.isSuccess()) {
            log.error("根据供应商ID查询分类信息【crm-api】异常, req = {}, error = {}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据表单编码获取表单组件列表
     *
     * @param
     * @return
     */
    public List<ComponentResp> getComponents(String formCode,Integer companyId, Long versionNumber){
        ComponentReq req = new ComponentReq();
        req.setFormCode(formCode);
        req.setCompanyId(companyId);
        req.setVersionNumber(versionNumber);
        Result<List<ComponentResp>> result = (Result<List<ComponentResp>>) iComponentAttrFeign.getComponents(req);
        if (!result.isSuccess()) {
            log.error("根据表单编码获取表单组件列表【iform-api】异常, req = {}, error = {}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }
    /**
     * 根据用户ID查询用户(批量)
     *
     * @param userIds
     * @return
     */
    public Map<Integer, UserInfoResp> batchUserById(List<Integer> userIds) {
        BatchUserReq req = new BatchUserReq();
        req.setIds(userIds);
        Result<List<UserInfoResp>> result = userService.batchUserById(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用user服务<根据id批量查询用户>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        Map<Integer, UserInfoResp> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(result.getData())) {
            map = result.getData().stream().collect(Collectors.toMap(UserInfoResp::getId, Function.identity(), (key1, key2) -> key2));
        }
        return map;
    }
    /**
     * 根据租户ID查询租户信息
     * @param tenantId
     * @return
     */
    public TenantInfoResp getTenantInfoOne(String tenantId){
        Set<String> tenantIds = Sets.newHashSet();
        tenantIds.add(tenantId);
        return batchQueryTenantDetail(tenantIds).get(0);
    }

    /**
     * 批量查询租户信息
     * @param tenantIds
     * @return
     */
    public List<TenantInfoResp> batchQueryTenantDetail(Set<String> tenantIds){
        BatchTenantDetailReq req = new BatchTenantDetailReq();
        req.setTenantIdSet(tenantIds);
        Result<List<TenantInfoResp>> result = tenantInfoFeignClient.batchQueryTenantDetail(req);
        if(!result.isSuccess()){
            log.error("batchQueryTenantDetail->tenantInfoFeignClient.batchQueryTenantDetail() 批量查询租户信息调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
        }
        return result.getData();
    }


    /**
     * 根据公司ID批量查询租户信息
     * @param companyId 公司ID
     * @return
     */
    public List<TenantInfoResp> queryTenantInfoListByCompanyId(Integer companyId){
        CompanyIdReq req = new CompanyIdReq();
        req.setCompanyId(companyId);
        Result<List<TenantInfoResp>> result = tenantInfoFeignClient.findCompanyTenant(req);
        if(!result.isSuccess()){
            log.error("queryTenantInfoByCompanyId->tenantInfoFeignClient.queryTenantInfoByCompanyId() 查询租户信息调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
        }
        return result.getData();
    }

    /**
     * 推送IM消息
     * @param receiveUserIds
     * @param receiveCompanyId
     * @param senderUserId
     * @param paramMap
     * @param nmsMsgTypeEnums
     * @param nmsTemplateEnums
     */
    @Async(value = "asyncServiceExecutor")
    public void sendImMsg(List<Integer> receiveUserIds, Integer receiveCompanyId,
                    Integer senderUserId, Map<String, Object> paramMap,
                    NmsMsgTypeEnums nmsMsgTypeEnums, NmsTemplateEnums nmsTemplateEnums){
        //组装调用发送消息参数
        IMMessageReq req = new IMMessageReq();
        req.setReceiveUsers(receiveUserIds);
        req.setMsgType(nmsMsgTypeEnums.getCode());
        req.setTemplateCode(nmsTemplateEnums.getCode());
        req.setToCompanyId(receiveCompanyId);
        req.setParamMap(paramMap);
        req.setSenderId(senderUserId);
        log.info("发送驳回消息推送req={}",JSON.toJSONString(req));
        Result result = imSendMsgFeign.messageSend(req);
        if(!result.isSuccess()){
            log.error("<Feign> 调用NMC服务<推送IM消息>异常 req={}, error={}", JSON.toJSONString(req), result.getMessage());
        }
    }


    /**
     * 根据权限code查询公司下有此权限的所有用户
     * @param companyId 公司Id
     * @param permCode 权限code
     * @return 用户Id
     */
    public List<Integer> queryPermCodeMember(Integer companyId, String permCode){
        Result<List<MemberSimpleResp>> result = permissionFeign.queryMemberInfoBy(companyId, permCode);
        if(!result.isSuccess()){
            log.error("<Feign> 调用system服务<根据公司ID及权限编码查询权限人>异常。参数：companyId={} permCode={}, error={}",companyId,permCode,result.getMessage());
        }else if(CollectionUtil.isEmpty(result.getData())){
            return null;
        }
        return result.getData().stream().map(memberSimpleResp -> memberSimpleResp.getUserId()).collect(Collectors.toList());
    }

    /**
     * 根据用户ID查询用户
     *
     * @param userId
     * @return
     */
    public UserInfoResp getUserById(Integer userId) {
        QueryUserReq req = new QueryUserReq();
        req.setId(userId);
        Result<UserInfoResp> result = userService.getUserById(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用user服务<根据用户主键查询用户>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 交换名片
     *
     * @param req
     * @return
     */
    public void addBusinessCardExchange(AddBusinessCardExchangeReq req) {
        Result result = iBusinessCardExchangeClient.addBusinessCardExchange(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用crm服务<交换名片>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
            throw new BusinessException(result.getCode(), result.getMessage());
        }
    }

    /**
     *  校验是否存在供应商
     *
     * @param companyId
     * @param targetCompanyId
     * @return
     */
    public boolean checkExistSupplier(Integer companyId, Integer targetCompanyId) {
        CheckSupplierReq req = new CheckSupplierReq();
        req.setCompanyId(companyId);
        req.setTargetCompanyId(targetCompanyId);
        Result<SupplierIsExistResp> result = iSupplierClient.checkExistSupplier(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用crm服务<校验是否存在供应商>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData().getFlag();
    }

    /**
     *  根据ID查询招标公示信息
     *
     * @param id
     * @return
     */
    public BiddingTrendsResp queryTrendsById(Integer id) {
        IdReq req = new IdReq();
        req.setId(id);
        Result<BiddingTrendsResp> result = iBiddingTrendsFeignClient.queryTrendsById(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用bidding服务<根据ID查询招标公示信息>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据联系人ID查询
     * @param id
     * @return
     */
    public List<ContactResp> queryContactList(Set<Integer> id){
        QueryContactListReq req = new QueryContactListReq();
        req.setContactIdSet(id);
        Result<List<ContactResp>> result = contactClient.queryListByBatchId(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用crm服务<根据联系人ID查询>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据联系人ID查询单个
     * @param id
     * @return
     */
    public ContactResp queryContactOne(Integer id){
        return queryContactList(com.google.common.collect.Sets.newHashSet(id)).get(0);
    }

    /**
     *  根据ID查询邮件模板信息
     *
     * @param ids
     * @return
     */
    public List<EmailTemplateInfoResp> batchQueryEmailTempById(List<Integer> ids) {
        QueryEmailTempReq req = new QueryEmailTempReq();
        req.setIds(ids);
        Result<List<EmailTemplateInfoResp>> result = iEMailFeign.queryEmailTempById(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用nms服务<根据ID查询邮件模板信息>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        if (CollectionUtil.isEmpty(result.getData())) {
            return new ArrayList<>();
        }
        return result.getData();
    }

    /**
     *  根据ID查询邮件模板信息
     *
     * @param id
     * @return
     */
    public EmailTemplateInfoResp queryEmailTempById(Integer id) {
        List<EmailTemplateInfoResp> list = batchQueryEmailTempById(Collections.singletonList(id));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     *  根据ID查询邮件smtp信息
     *
     * @param ids
     * @return
     */
    public List<EmailSmtpConfigInfoResp> batchQueryEmailSmtpById(List<Integer> ids) {
        QueryEmailSmtpReq req = new QueryEmailSmtpReq();
        req.setIds(ids);
        Result<List<EmailSmtpConfigInfoResp>> result = iEMailFeign.queryEmailSmtpById(req);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用nms服务<根据ID查询邮件模板信息>异常。参数：req={}  error={}", JSONObject.toJSONString(req), result.getMessage());
        }
        if (CollectionUtil.isEmpty(result.getData())) {
            return new ArrayList<>();
        }
        return result.getData();
    }

    /**
     *  根据ID查询邮件smtp信息
     *
     * @param id
     * @return
     */
    public EmailSmtpConfigInfoResp queryEmailSmtpById(Integer id) {
        List<EmailSmtpConfigInfoResp> list = batchQueryEmailSmtpById(Collections.singletonList(id));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
}
