/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoPrintConfigDao;
import com.echronos.expo.model.ExpoPrintConfig;
import org.springframework.stereotype.Component;


/**
 * ExpoPrintConfig Manager
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class ExpoPrintConfigManager extends ServiceImpl<ExpoPrintConfigDao, ExpoPrintConfig> {

    /**
     * 查询详情
     *
     * @param dto
     * @return
     */
    public ExpoPrintConfig queryOne(ExpoPrintConfig dto) {
        LambdaQueryWrapper<ExpoPrintConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoPrintConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq((dto.getExpoId() != null), ExpoPrintConfig::getExpoId, dto.getExpoId());
        return getOne(queryWrapper);
    }

    /**
     * 删除
     *
     * @param id
     */
    public void delById(Integer id) {
        LambdaUpdateWrapper<ExpoPrintConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpoPrintConfig::getExpoId, id)
                .eq(ExpoPrintConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoPrintConfig::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        this.update(updateWrapper);
    }
}
