/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoExhibitorEmailRecords;
import com.echronos.expo.dao.ExpoExhibitorEmailRecordsDao;

import java.util.List;


/**
 * ExpoExhibitorEmailRecords Manager
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Component
public class ExpoExhibitorEmailRecordsManager extends ServiceImpl<ExpoExhibitorEmailRecordsDao, ExpoExhibitorEmailRecords> {

    /**
     * 根据展商ID查询自动发送记录
     * @param exhibitorIds 展商ID
     * @param autoRuleIds 自动规则ID
     * @return
     */
    public List<ExpoExhibitorEmailRecords> getAutoSendList(List<Integer> exhibitorIds, List<Integer> autoRuleIds){
        LambdaQueryWrapper<ExpoExhibitorEmailRecords> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorEmailRecords>()
                .in(ExpoExhibitorEmailRecords::getExhibitorId, exhibitorIds)
                .in(ExpoExhibitorEmailRecords::getAutoRuleId, autoRuleIds)
                .eq(ExpoExhibitorEmailRecords::getIsAutoSend, CommonStatus.YesOrNoEnum.YES.getValue())
                .eq(ExpoExhibitorEmailRecords::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

}
