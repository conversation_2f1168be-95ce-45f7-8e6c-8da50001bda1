/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.model.BaseNotTenantEntity;
import com.echronos.expo.model.ExpoExhibitorBooth;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoTravelOrder;
import com.echronos.expo.dao.ExpoTravelOrderDao;

import java.util.List;


/**
 * ExpoTravelOrder Manager
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Component
public class ExpoTravelOrderManager extends ServiceImpl<ExpoTravelOrderDao, ExpoTravelOrder> {

    /**
     * 获取展商展位信息
     * @param exhibitorId 展商ID
     * @return 展商展位信息
     */
    public List<ExpoTravelOrder> getListByExhibitorId(Integer exhibitorId) {
        LambdaQueryWrapper<ExpoTravelOrder> queryWrapper = new LambdaQueryWrapper<ExpoTravelOrder>()
                .eq(ExpoTravelOrder::getExhibitorId, exhibitorId)
                .eq(ExpoTravelOrder::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

}
