/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoChannel;
import com.echronos.expo.dao.ExpoChannelDao;

import java.util.List;


/**
 * EchExpoChannel Manager
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class ExpoChannelManager extends ServiceImpl<ExpoChannelDao, ExpoChannel> {

    /**
     * 根据渠道来源获取渠道信息
     * @param source
     * @param companyId
     * @return
     */
     public ExpoChannel getOneBySource(Integer source, Integer companyId) {
         LambdaQueryWrapper<ExpoChannel> queryWrapper = new LambdaQueryWrapper<ExpoChannel>()
                 .eq(ExpoChannel::getChannelSource, source)
                 .eq(ExpoChannel::getCompanyId, companyId);
          return baseMapper.selectOne(queryWrapper);
     }

    /**
     * 根据id获取渠道信息
     * @param id
     * @return
     */
     public ExpoChannel getId(Integer id){
         LambdaQueryWrapper<ExpoChannel> queryWrapper = new LambdaQueryWrapper<ExpoChannel>()
                 .eq(ExpoChannel::getId, id);
         return baseMapper.selectOne(queryWrapper);
     }

    /**
     * 根据公司id和渠道名称获取渠道信息
     * @param companyId
     * @param channelNameList
     * @return
     */
     public List<ExpoChannel> getList(Integer companyId, List<String> channelNameList){
         LambdaQueryWrapper<ExpoChannel> queryWrapper = new LambdaQueryWrapper<ExpoChannel>()
                 .eq(ExpoChannel::getCompanyId, companyId)
                 .in(CollectionUtils.isNotEmpty(channelNameList), ExpoChannel::getChannelName, channelNameList);
         return list(queryWrapper);
     }


}
