/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoEmailRuleConfigDao;
import com.echronos.expo.dto.ExpoEmailRuleConfigDTO;
import com.echronos.expo.model.ExpoEmailRuleConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * ExpoEmailRuleConfig Manager
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Component
public class ExpoEmailRuleConfigManager extends ServiceImpl<ExpoEmailRuleConfigDao, ExpoEmailRuleConfig> {

    /**
     * 查询规则信息
     *
     * @param dto
     * @return
     */
    public ExpoEmailRuleConfig queryBy(ExpoEmailRuleConfigDTO dto) {
        LambdaQueryWrapper<ExpoEmailRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getExpoId(), ExpoEmailRuleConfig::getExpoId, dto.getExpoId())
                .eq(null != dto.getEventId(), ExpoEmailRuleConfig::getEventId, dto.getEventId())
                .ne(null != dto.getIgnoreId(), ExpoEmailRuleConfig::getId, dto.getIgnoreId())
                .eq(StringUtils.isNotBlank(dto.getRuleName()), ExpoEmailRuleConfig::getRuleName, dto.getRuleName());
        return this.getOne(queryWrapper);
    }

    /**
     * 查询规则信息
     *
     * @param dto
     * @return
     */
    public ExpoEmailRuleConfig queryOne(ExpoEmailRuleConfigDTO dto) {
        LambdaQueryWrapper<ExpoEmailRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getId(), ExpoEmailRuleConfig::getId, dto.getId())
                .eq(null != dto.getCompanyId(), ExpoEmailRuleConfig::getCompanyId, dto.getCompanyId())
                .eq(null != dto.getExpoId(), ExpoEmailRuleConfig::getExpoId, dto.getExpoId())
                .eq(null != dto.getRuleType(), ExpoEmailRuleConfig::getRuleType, dto.getRuleType());
        return this.getOne(queryWrapper);
    }

    /**
     * 批量查询规则信息
     *
     * @param dto
     * @return
     */
    public List<ExpoEmailRuleConfig> queryList(ExpoEmailRuleConfigDTO dto) {
        LambdaQueryWrapper<ExpoEmailRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getExpoId(), ExpoEmailRuleConfig::getExpoId, dto.getExpoId())
                .eq(null != dto.getEventId(), ExpoEmailRuleConfig::getEventId, dto.getEventId())
                .ne(null != dto.getIgnoreId(), ExpoEmailRuleConfig::getId, dto.getIgnoreId())
                .eq(null != dto.getCompanyId(), ExpoEmailRuleConfig::getCompanyId, dto.getCompanyId());
        return this.list(queryWrapper);
    }

    /**
     * 分页查询规则信息
     *
     * @param dto
     * @return
     */
    public Page<ExpoEmailRuleConfig> pageList(ExpoEmailRuleConfigDTO dto) {
        LambdaQueryWrapper<ExpoEmailRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(null != dto.getExpoId(), ExpoEmailRuleConfig::getExpoId, dto.getExpoId())
                .eq(null != dto.getRuleType(), ExpoEmailRuleConfig::getRuleType, dto.getRuleType())
                .eq(null != dto.getCompanyId(), ExpoEmailRuleConfig::getCompanyId, dto.getCompanyId());
        return this.page(new Page<>(dto.getPageNo(), dto.getPageSize()), queryWrapper);
    }

    /**
     * 根据ID移除
     *
     * @param id
     * @param updateUser
     */
    public void delById(Integer id, Integer updateUser) {
        LambdaUpdateWrapper<ExpoEmailRuleConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoEmailRuleConfig::getId, id)
                .set(ExpoEmailRuleConfig::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(ExpoEmailRuleConfig::getUpdateUser, updateUser);
        this.update(updateWrapper);
    }
}
