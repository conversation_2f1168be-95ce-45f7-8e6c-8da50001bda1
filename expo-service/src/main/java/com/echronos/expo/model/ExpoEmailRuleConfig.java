/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoEmailRuleConfig 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@TableName("ech_expo_email_rule_config")
public class ExpoEmailRuleConfig{


    /**
     *  主键ID 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  规则名称 
     */
    private String ruleName;
    /**
     *  触发事件ID 
     */
    private Integer eventId;
    /**
     *  模板ID 
     */
    private Integer templateId;
    /**
     *  规则类型：0-观众自动发送规则，1-展商自动发送规则 
     */
    private Integer ruleType;
    /**
     *  触发天数（提前或延后的天数） 
     */
    private Integer triggerDay;
    /**
     *  发送时间（格式：HH:mm:ss） 
     */
    private LocalDateTime sendTime;
    /**
     *  SMTP配置ID 
     */
    private Integer smtpConfigId;
    /**
     *  收件观众角色：1普通观众 2专业买家 3全部观众
     */
    private Integer audienceRole;
    /**
     *  状态(0:禁用,1:启用) 
     */
    private Integer status;
    /**
     *  公司ID 
     */
    private Integer companyId;
    /**
     *  是否删除(1:已删除,0:未删除) 
     */
    private Integer isDeleted;
    /**
     *  创建人ID 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  更新人ID 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  更新时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}