/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ExpoChannelConfig 实体类
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@TableName("ech_expo_channel_config")
public class ExpoChannelConfig extends BaseEntity {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 渠道ID
     */
    private Integer channelId;
    /**
     * 表单ID
     */
    private Integer formId;

    /**
     * 渠道类型
     */
    private Integer channelType;
}