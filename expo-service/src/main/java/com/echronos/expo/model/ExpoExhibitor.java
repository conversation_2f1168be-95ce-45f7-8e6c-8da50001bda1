/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoExhibitor 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("ech_expo_exhibitor")
public class ExpoExhibitor extends BaseNotTenantEntity{

    /**
     * 展会id
     */
    private Integer expoId;
    /**
     *  客户id 
     */
    private Integer customerId;
    /**
     *  客户公司id 
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id 
     */
    private Integer customerContactsId;
    /**
     *  客户联系人名称
     */
    private String customerContactsName;
    /**
     *  客户联系人手机
     */
    private String customerContactsPhone;
    /**
     *  客户联系人邮箱
     */
    private String customerContactsEmail;
    /**
     *  业务员id 
     */
    private Integer businessMemberId;
    /**
     *  展位费用 
     */
    private BigDecimal boothTotalAmount;
    /**
     *  租赁费用 
     */
    private BigDecimal leaseTotalAmount;
    /**
     *  合同状态：10.待签署  20.已签署 
     */
    private Integer contractStatus;
    /**
     *  邮件发送状态：10.未发送  20.已发送 
     */
    private Integer sendEmailStatus;
    /**
     *  企业信息收集状态：10.待提交  20.待审核 30.审核拒绝 40.审核通过 
     */
    private Integer enterpriseInfoStatus;
    /**
     *  会刊信息收集状态： 10.待提交  20.待审核  30.审核拒绝  40.审核通过 
     */
    private Integer journalInfoStatus;
    /**
     *  租赁类型：10.未收集  20.不需要租赁  30.需要租赁 
     */
    private Integer leaseDemandType;
    /**
     *  备注 
     */
    private String remark;
    /**
     * 是否开启预约：0-否 1-是
     */
    private Integer isAppoint;
}