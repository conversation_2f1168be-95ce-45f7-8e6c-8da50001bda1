/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * ExpoAudience 实体类
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("ech_expo_audience")
public class ExpoAudience {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 渠道id
     */
    private Integer channelId;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 关联客户系统id
     */
    private Integer customerId;
    /**
     * 关联客户公司id
     */
    private Integer customerCompanyId;
    /**
     * 自定义表单code
     */
    private String formCode;
    /**
     * 自定义表单版本
     */
    private String versionNumber;

    /**
     * 同步crm
     */
    private Boolean isCrm;

    /**
     * 成员id
     */
    private Integer memberId;
    /**
     * ip地址
     */
    private String ip;

    /**
     * 是否开启预约：0-否 1-是
     */
    private Integer isAppoint;

    /**
     * 是否是买家：0-否 1-是
     */
    private Integer isBuyer;
    /**
     * 二维码打印次数
     */
    private Integer qrCodePrintCount;
    /**
     * 公司网址
     */
    private String companyWebsite;

    @TableField(exist = false)
    private Map<String, String> extMap;

    /**
     * 表单id
     */
    @TableField(exist = false)
    private Integer formId;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改者id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 是否删除0否1是
     */
    @TableLogic(
            value = "0",
            delval = "1"
    )
    private Boolean isDeleted;

    @TableField(
            fill = FieldFill.INSERT
    )
    private String tenantId;
}