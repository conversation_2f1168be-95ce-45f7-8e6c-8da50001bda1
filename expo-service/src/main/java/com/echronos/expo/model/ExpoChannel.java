/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ExpoChannel 实体类
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("ech_expo_channel")
public class ExpoChannel extends BaseEntity {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道类型
     * 1、观众
     * 2、参展商
     */
    private Integer channelType;

    /**
     * 渠道来源：0.系统  1.展商
     */
    private Integer channelSource;

    /**
     * 来源公司ID
     */
    private Integer sourceCompanyId;

}