/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * EchExpoInfo 实体类
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("ech_expo_info")
public class ExpoInfo extends BaseEntity {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会名称
     */
    private String expoName;
    /**
     * 展会简称
     */
    private String shortName;
    /**
     * 国家code
     */
    private Integer countryCode;
    /**
     * 城市code
     */
    private Integer cityCode;

    /**
     * 城市时区
     */
    private String zoneId;
    /**
     * 展馆名称
     */
    private String hallName;

    /**
     * 举办方名称
     */
    private String organizer;

    /**
     * 详细地址
     */
    private String address;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 城市所在时区开始时间
     */
    private LocalDateTime zoneIdStartTime;
    /**
     * 城市所在时区结束时间
     */
    private LocalDateTime zoneIdEndTime;
    /**
     * 展会状态：0、未开始；1、进行中；2、已结束
     */
    private Integer expoStatus;
    /**
     * remark
     */
    private String remark;
}