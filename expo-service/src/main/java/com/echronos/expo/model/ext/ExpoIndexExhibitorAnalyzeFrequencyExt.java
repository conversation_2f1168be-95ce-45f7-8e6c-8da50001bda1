package com.echronos.expo.model.ext;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 展会展商参展频率
 * <AUTHOR>
 * @date 2025-08-20 21:03
 */
@Data
public class ExpoIndexExhibitorAnalyzeFrequencyExt {

    /**
     * 首次参展次数
     */
    private BigDecimal oneFrequency;
    /**
     * 参展2次次数
     */
    private BigDecimal twoFrequency;
    /**
     * 参展3次次数
     */
    private BigDecimal threeFrequency;
    /**
     * 参展3次以上次数
     */
    private BigDecimal fourFrequency;

}
