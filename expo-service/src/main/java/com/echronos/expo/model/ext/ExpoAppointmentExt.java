package com.echronos.expo.model.ext;

import com.echronos.expo.model.ExpoAppointment;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/8/11 20:07
 */
@Data
public class ExpoAppointmentExt extends ExpoAppointment {
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 预约类型：1.我预约的 2.预约我的
     */
    private Integer appointType;
    /**
     * 展商ID
     */
    private Integer exhibitorId;
    /**
     * 被预约人
     */
    private Integer appointedMemberId;
    /**
     * 展会公司ID
     */
    private Integer expoCompanyId;
    /**
     * 展商名/观众名
     */
    private String name;
    /**
     * 观众所属公司名
     */
    private String audienceCompanyName;
    /**
     * 观众ID
     */
    private Integer audienceId;
}
