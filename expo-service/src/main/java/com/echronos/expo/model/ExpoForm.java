/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ExpoForm 实体类
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("ech_expo_form")
public class ExpoForm extends BaseEntity {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 表单分组类型：1.观众相关表单  2.展商相关表单
     */
    private Integer formGroup;
    /**
     * 表单类型：1.观众注册表单，2.观众满意度调查 20.企业信息收集  21.会刊收集  22.满意度调查  23.其它
     */
    private Integer formType;
    /**
     * 表单名称
     */
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 表单图片
     */
    private String formImageUrl;
    /**
     * 发布站点id
     */
    private String publishTenantId;
    /**
     * 自定义表单系统code
     */
    private String formCode;
    /**
     * 自定义表单版本号
     */
    private Long versionNumber;
    /**
     * 是否启用
     */
    private Integer isEnable;
    /**
     *  表单控件
     */
    private String fieldJson;
}