package com.echronos.expo.model.ext;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2025/8/18 20:13
 */
@Data
public class ExpoAppointmentCountExt {
    /**
     * 展商ID
     */
    private Integer exhibitorId;
    /**
     * 预约日期
     */
    private LocalDate appointmentDate;
    /**
     * 预约小时
     */
    private LocalTime appointmentHour;
    /**
     * 预约次数
     */
    private Integer appointmentCount;
    /**
     * 待确认预约数
     */
    private Integer pendingCount;
    /**
     * 已接受预约数
     */
    private Integer acceptedCount;
    /**
     * 已拒绝预约数
     */
    private Integer rejectedCount;
    /**
     * 观众预约展商数
     */
    private Integer audienceAppointCount;
    /**
     * 展商预约观众数
     */
    private Integer exhibitorAppointCount;
}
