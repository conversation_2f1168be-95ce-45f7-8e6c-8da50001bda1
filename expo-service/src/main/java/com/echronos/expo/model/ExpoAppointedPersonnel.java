/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ExpoAppointedPersonnel 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@TableName("ech_expo_appointed_personnel")
public class ExpoAppointedPersonnel{


    /**
     *  主键 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  业务ID 
     */
    private Integer businessId;
    /**
     *  业务类型：1展商 2观众 
     */
    private Integer businessType;
    /**
     *  用户ID
     */
    private Integer userId;
    /**
     *  成员ID 
     */
    private Integer memberId;
    /**
     *  公司ID 
     */
    private Integer companyId;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  更新人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否已删除：0否 1是 
     */
    private Integer isDeleted;
    /**
     *  预约时间列表
     */
    @TableField(exist = false)
    private List<ExpoAppointedPersonnelTime> timeList;
}