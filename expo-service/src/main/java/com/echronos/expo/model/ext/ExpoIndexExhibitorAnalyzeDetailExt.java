package com.echronos.expo.model.ext;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 展商分析明细
 * <AUTHOR>
 * @date 2025-08-20 20:10
 */
@Data
public class ExpoIndexExhibitorAnalyzeDetailExt {

    /**
     * 展商客户ID
     */
    private Integer customerId;
    /**
     * 参展次数
     */
    private Integer total;
    /**
     * 最新参加时间
     */
    private LocalDateTime recentlyJoinTime;

}
