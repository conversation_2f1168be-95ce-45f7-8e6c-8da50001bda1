/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoExhibitorEmailRecords 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@TableName("ech_expo_exhibitor_email_records")
public class ExpoExhibitorEmailRecords{


    /**
     *  ID 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     *  所属公司id 
     */
    private Integer companyId;
    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  展商ID 
     */
    private Integer exhibitorId;
    /**
     *  电子邮箱 
     */
    private String email;
    /**
     *  发送邮件内容 
     */
    private String content;
    /**
     *  邮件参数 
     */
    private String paramJson;
    /**
     *  邮件系统发送流水号 
     */
    private String businessCode;
    /**
     *  是否自动发送类型：0.否  1.是 
     */
    private Integer isAutoSend;
    /**
     *  关联自动发送规则id 
     */
    private Integer autoRuleId;
    /**
     *  发送成功：true、是，false、否 
     */
    private Integer isSend;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  更新时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  更新人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  是否删除：0否 1是 
     */
    private Integer isDeleted;
    /**
     *  租户id 
     */
    private String tenantId;
}