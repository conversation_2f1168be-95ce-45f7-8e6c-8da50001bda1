package com.echronos.expo.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/22
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PreventDuplication {

    /**
     * 模块
     */
    String module() default "";

    /**
     * 过期秒数,默认为5秒
     */
    int expire() default 5;

    /**
     * 超时时间单位，默认为秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * key是否要拼接用户id
     */
    boolean keyJointUserFlag() default false;

    /**
     * 重复请求的提醒信息
     */
    String msg() default "操作频繁，请稍后再试！";
}
