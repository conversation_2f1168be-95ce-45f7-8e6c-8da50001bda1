package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/26 20:09
 */
@Data
public class ExpoEmailRuleDetailParam {
    /**
     * 规则ID
     */
    @NotNull(message = "{NOTNULL.RULE.ID}")
    private Integer id;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 规则类型(0-观众自动发送规则，1-展商自动发送规则)
     */
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;
}
