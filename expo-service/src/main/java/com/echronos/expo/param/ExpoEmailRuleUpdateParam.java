package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2025/8/26 16:39
 */
@Data
public class ExpoEmailRuleUpdateParam {
    /**
     * 规则ID
     */
    @NotNull(message = "{NOTNULL.RULE.ID}")
    private Integer id;

    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;

    /**
     * 规则名称
     */
    @Size(max = 100, message = "规则名称不能超过100个字")
    @NotBlank(message = "{NOTBLANK.RULE.RULENAME}")
    private String ruleName;

    /**
     * 触发事件ID
     */
    @NotNull(message = "{NOTNULL.RULE.EVENTID}")
    private Integer eventId;

    /**
     * 使用模板ID
     */
    @NotNull(message = "{NOTNULL.RULE.TEMPLATEID}")
    private Integer templateId;

    /**
     * 触发天数（提前或延迟的天数）
     */
    private Integer triggerDay;

    /**
     * 触发时间（HH：mm：ss）
     */
    private LocalTime sendTime;

    /**
     * SMTP配置ID
     */
    @NotNull(message = "smtp配置不能为空")
    private Integer  smtpConfigId;

    /**
     * 状态(0:启用,1:禁用)
     */
    @NotNull(message = "{NOTNULL.RULE.STATUS}")
    private Integer status;

    /**
     * 收件观众角色：1普通观众 2专业买家 3全部观众
     */
    private Integer audienceRole;

    /**
     * 规则类型(0-观众自动发送规则，1-展商自动发送规则)
     */
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;
}
