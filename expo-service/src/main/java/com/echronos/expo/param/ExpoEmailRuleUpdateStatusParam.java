package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/26 17:21
 */
@Data
public class ExpoEmailRuleUpdateStatusParam {
    /**
     * 规则ID
     */
    @NotNull(message = "{NOTNULL.RULE.ID}")
    private Integer id;
    /**
     * 状态(0:禁用,1:启用)
     */
    @NotNull(message = "{NOTNULL.RULE.STATUS}")
    private Integer status;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 规则类型(0-观众自动发送规则，1-展商自动发送规则)
     */
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;
}
