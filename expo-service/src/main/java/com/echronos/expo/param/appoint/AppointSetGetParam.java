package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:58
 */
@Data
public class AppointSetGetParam {
    /**
     * 观众ID/展商ID
     * 观众端传观众ID，展商端传展商ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
}
