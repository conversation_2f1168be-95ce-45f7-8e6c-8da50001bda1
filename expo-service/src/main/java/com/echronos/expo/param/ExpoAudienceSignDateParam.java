package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-08-18 10:32
 */
@Data
public class ExpoAudienceSignDateParam {

    /**
     * 展会id
     */
    @NotNull(message = "展会id不能为空")
    private Integer expoId;
    /**
     *  时间日期
     */
    @NotNull(message = "时间日期不能为空")
    private LocalDateTime time;

}
