package com.echronos.expo.param;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-04 13:38
 */
@Data
public class ExpoAttachmentFileParam {

    /**
     *  主键
     */
    private Integer id;
    /**
     *  展会id
     */
    private Integer expoId;
    /**
     *  业务id
     */
    private Integer businessId;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     *  文件名称
     */
    private String fileName;
    /**
     *  文件大小
     */
    private Integer fileSize;
    /**
     *  附件地址
     */
    private String filePath;
    /**
     *  展会附件类型：1.展会手册  2.展会海报模板  3.展位订单
     */
    private Integer type;
    /**
     * 订单文件类型：0.图片  1.文件
     */
    private Integer orderFileType;

}
