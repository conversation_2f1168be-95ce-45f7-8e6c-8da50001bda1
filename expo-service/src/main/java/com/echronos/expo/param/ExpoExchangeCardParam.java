package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025/8/23 17:17
 */
@Data
public class ExpoExchangeCardParam {
    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
    /**
     * 业务类型：1展商 2需求
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;
    /**
     * 留言
     */
    @Size(max = 60, message = "留言长度不能超过60个字符")
    private String remark;
}
