package com.echronos.expo.param.exhibitor;

import com.echronos.expo.param.ExpoAttachmentFileParam;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-13 16:00
 */
@Data
public class AddLeaseOrderParam {

    /**
     * 展会ID
     */
    @NotNull(message = "展会id不能为空")
    private Integer expoId;

    /**
     * 展商ID
     */
    @NotNull(message = "展商Id不能为空")
    private Integer id;

    /**
     * 开单时间
     */
    @NotNull(message = "开单时间不能为空")
    private LocalDateTime orderTime;

    /**
     * 业务员ID
     */
    @NotNull(message = "业务员不能为空")
    private Integer businessMemberId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目不能为空")
    private Integer projectId;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 附件
     */
    private List<ExpoAttachmentFileParam> attachmentList;

    /**
     * 订单明细列表
     */
    @Valid
    @NotNull(message = "订单明细不能为空")
    @Size(min = 1, message = "至少需要选择一个商品")
    private List<AddLeaseOrderDetailParam> orderDetailList;

}
