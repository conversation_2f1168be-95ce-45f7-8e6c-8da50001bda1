package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ExpoFormConfigParam {

    /**
     *  表单ID
     */
    @NotNull(message = "表单ID不能为空")
    private Integer iformId;
    /**
     *  展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 选择展示字段集合
     */
    @NotNull(message = "{NOT.NULL.EXPO.FIELD}")
    private List<String> fieldList;
}
