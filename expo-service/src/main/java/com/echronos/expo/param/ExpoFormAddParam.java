/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.param;


import com.echronos.expo.vo.FormFieldVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * EchExpoForm controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoFormAddParam {
    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 表单分组类型：1.观众相关表单  2.展商相关表单
     */
    @NotNull(message = "{NOT.NULL.EXPO.FROM.GROUP}")
    private Integer formGroup;
    /**
     * 表单类型：1.观众注册表单，2.观众满意度调查 20.企业信息收集  21.会刊收集  22.满意度调查  23.其它
     */
    @NotNull(message = "{NOT.NULL.EXPO.FROM.TYPE}")
    private Integer formType;
    /**
     * 表单名称
     */
    @NotBlank(message = "{NOT.NULL.EXPO.FORM.NAME}")
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 表单图片
     */
    private String formImageUrl;
    /**
     * 发布站点id
     */
    private String publishTenantId;
    /**
     * 自定义表单系统code
     */
    @NotBlank(message = "自定义code不能为空")
    private String formCode;
    /**
     * 自定义表单版本号
     */
    private Long versionNumber;
    /**
     * 是否启用：0否 1是
     */
    @NotNull(message = "是否启用不能为空")
    private Integer isEnable;
}