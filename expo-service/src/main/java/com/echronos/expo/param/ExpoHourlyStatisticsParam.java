package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 每小时互动分布统计参数
 *
 * <AUTHOR>
 * @date 2025/8/18 16:35
 */
@Data
public class ExpoHourlyStatisticsParam {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;

    /**
     * 日期
     */
    @NotBlank(message = "日期不能为空")
    private String date;
}
