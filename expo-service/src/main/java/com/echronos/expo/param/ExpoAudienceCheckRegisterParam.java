package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-08-19 20:29
 */
@Data
public class ExpoAudienceCheckRegisterParam {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 邮箱
     */
    @NotBlank(message = "{NOT.NULL.EXPO.EMAIL}")
    private String email;

}
