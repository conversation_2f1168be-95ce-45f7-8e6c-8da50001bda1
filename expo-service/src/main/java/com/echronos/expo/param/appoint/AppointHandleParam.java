package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/12 16:22
 */
@Data
public class AppointHandleParam {
    /**
     * 预约ID
     */
    @NotNull(message = "ID不能为空")
    private Integer id;
    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 业务ID（展商ID/观众ID）
     * 观众端传观众ID，展商端传展商ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
}
