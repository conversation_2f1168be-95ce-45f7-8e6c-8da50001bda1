package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/9 15:37
 */
@Data
public class AppointedPersonnelListParam {
    /**
     * 展商ID（观众预约展商必传）
     */
    private Integer businessId;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 业务类型：1展商 2观众
     * 观众预约展商传1，展商预约观众传2
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
}
