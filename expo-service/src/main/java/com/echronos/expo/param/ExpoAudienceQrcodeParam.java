package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取观众二维码信息
 * <AUTHOR>
 * @date 2025-08-06 14:19
 */
@Data
public class ExpoAudienceQrcodeParam {

    /**
     * 观众ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer id;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer expoId;
    /**
     * 公司id
     */
    @NotNull(message = "{NOT.NULL.EXPO.COMPANYID}")
    private Integer companyId;

}
