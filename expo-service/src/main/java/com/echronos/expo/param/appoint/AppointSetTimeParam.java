package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 20:31
 */
@Data
public class AppointSetTimeParam {
    /**
     * 预约人员ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer id;
    /**
     * 预约时间段
     */
    @Valid
    private List<TimeSlotParam> timeList;
    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 业务ID（展商ID/观众ID）
     * 观众端传观众ID，展商端传展商ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
}
