package com.echronos.expo.param;

import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/20 14:19
 */
@Data
public class ExpoExhibitorStatisticsPageParam extends BasePageReq {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;
    /**
     * 展商ID
     */
    @NotNull(message = "展商ID不能为空")
    private Integer exhibitorId;
}
