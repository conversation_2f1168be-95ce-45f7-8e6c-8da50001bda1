package com.echronos.expo.param.appoint;

import com.echronos.expo.param.ExpoAttachmentFileParam;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/8/11 10:07
 */
@Data
public class AppointSubmitParam {
    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 业务ID（展商/观众）
     * 展商预约观众必须传
     */
    private Integer businessId;
    /**
     * 预约人员ID
     */
    @NotNull(message = "预约人员ID不能为空")
    private Integer appointedPersonnelId;
    /**
     * 预约目的（展商/观众）
     */
    @NotNull(message = "预约目的不能为空")
    private Integer purposeType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 预约时间段ID
     */
    @NotEmpty(message = "预约时间段ID不能为空")
    private Set<Integer> timeIds;
    /**
     * 附件
     */
    private List<ExpoAttachmentFileParam> attachmentList;
}
