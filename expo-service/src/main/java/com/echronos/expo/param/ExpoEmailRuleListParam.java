package com.echronos.expo.param;

import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/26 17:05
 */
@Data
public class ExpoEmailRuleListParam extends BasePageReq {
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 规则类型(0-观众自动发送规则，1-展商自动发送规则)
     */
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;
}
