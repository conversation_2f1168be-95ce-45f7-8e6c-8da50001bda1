package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/20 14:25
 */
@Data
public class AppointDetailListParam {
    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;
    /**
     * 业务类型：1展商预约观众 2观众预约展商
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝
     */
    private Integer status;
    /**
     * 搜索关键词
     */
    private String keywords;
}
