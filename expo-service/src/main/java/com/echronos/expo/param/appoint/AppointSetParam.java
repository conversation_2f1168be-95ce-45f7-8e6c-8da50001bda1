package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/7 20:11
 */
@Data
public class AppointSetParam {
    /**
     * 观众ID/展商ID
     * 观众端传观众ID，展商端传展商ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer id;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 是否开启预约：0-否 1-是
     */
    @NotNull(message = "是否开启预约不能为空")
    private Integer isAppoint;
}
