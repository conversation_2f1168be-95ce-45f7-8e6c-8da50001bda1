package com.echronos.expo.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 观众导入
 *
 * <AUTHOR>
 * @Date 2025/5/19 14:01
 * @ClassName ExpoAudienceImportParam
 */
@Data
public class ExpoAudienceImportParam {
    @JSONField(serialize = false)
    private MultipartFile file;

    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;

    /**
     * 浏览器时区
     */
    private String zoneId;

    /**
     * 执行类型：1-跳过错误行并继续导入，2-更新重复项(仅修改非空字段)，3-覆盖重复项(完全替换旧数据)，4-跳过重复项(保留原数据)
     */
    private Integer executeType;

}
