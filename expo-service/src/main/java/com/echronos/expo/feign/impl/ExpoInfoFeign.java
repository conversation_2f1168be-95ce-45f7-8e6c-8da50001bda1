package com.echronos.expo.feign.impl;

import com.echronos.commons.Result;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.api.feign.IExpoInfoFeign;
import com.echronos.expo.api.req.ExpoIdReq;
import com.echronos.expo.api.resp.ExpoInfoResp;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ExpoInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-08-18 20:10
 */
@Slf4j
@RestController
public class ExpoInfoFeign implements IExpoInfoFeign {

    @Resource
    private ExpoInfoManager expoInfoManager;

    @Override
    public Result<ExpoInfoResp> getExpoInfoById(ExpoIdReq req) {
        ExpoInfo expoInfo = expoInfoManager.getById(req.getExpoId());
        ExpoInfoResp expoInfoResp = null;
        if(null != expoInfo){
            expoInfoResp = CopyObjectUtils.copyAtoB(expoInfo, ExpoInfoResp.class);
        }
        return Result.build(expoInfoResp);
    }

}
