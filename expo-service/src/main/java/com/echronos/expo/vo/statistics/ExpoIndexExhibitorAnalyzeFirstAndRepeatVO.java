package com.echronos.expo.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-21 10:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpoIndexExhibitorAnalyzeFirstAndRepeatVO {

    /**
     * 新增参展商
     */
    private BigDecimal firstTotal;
    /**
     * 复展商
     */
    private BigDecimal repeatTotal;

}
