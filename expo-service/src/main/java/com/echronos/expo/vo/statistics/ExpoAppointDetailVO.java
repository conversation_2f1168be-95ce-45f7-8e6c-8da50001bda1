package com.echronos.expo.vo.statistics;

import com.echronos.expo.vo.appoint.ExpoAppointmentTimeVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/20 20:48
 */
@Data
public class ExpoAppointDetailVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型：1展商 2观众
     */
    private Integer businessType;
    /**
     * 展商名称
     */
    private String exhibitorName;
    /**
     * 观众名称
     */
    private String audienceName;
    /**
     * 预约目的（展商/观众）
     */
    private Integer purposeType;
    /**
     * 预约目的名称（展商/观众）
     */
    private String purposeName;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝 40.已取消
     */
    private Integer status;
    /**
     * 状态名称
     */
    private String statusName;
    /**
     * 预约时间列表
     */
    private List<ExpoAppointmentTimeVO> timeList;
    /**
     * 展位
     */
    private String boothNumberStr;
    /**
     * 备注
     */
    private String remark;
}
