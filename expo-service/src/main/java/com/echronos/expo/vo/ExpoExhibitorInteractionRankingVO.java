package com.echronos.expo.vo;

import lombok.Data;

/**
 * 互动展商排行统计VO
 *
 * <AUTHOR>
 * @date 2025/8/18 17:30
 */
@Data
public class ExpoExhibitorInteractionRankingVO {
    /**
     * 展商ID
     */
    private Integer exhibitorId;
    /**
     * 客户公司ID
     */
    private Integer customerCompanyId;
    /**
     * 展商名称
     */
    private String exhibitorName;
    /**
     * 展商扫码次数
     */
    private Integer scanCount;
    /**
     * 观众预约次数
     */
    private Integer appointmentCount;
    /**
     * 互动次数
     */
    private Integer totalInteraction;
}
