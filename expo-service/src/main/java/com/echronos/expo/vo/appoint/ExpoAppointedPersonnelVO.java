package com.echronos.expo.vo.appoint;

import lombok.Data;

import java.util.List;

/**
 * ExpoAppointedPersonnel controller层返回值
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ExpoAppointedPersonnelVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型：1展商 2观众
     */
    private Integer businessType;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     * 成员名称
     */
    private String memberName;
    /**
     * 职位
     */
    private String position;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 已设置时间段数
     */
    private Integer timeSize;
    /**
     * 已设置时间段列表
     */
    private List<ExpoAppointedPersonnelTimeVO> timeList;
}