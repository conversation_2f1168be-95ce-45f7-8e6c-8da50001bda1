package com.echronos.expo.vo.appoint;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/11 14:45
 */
@Data
public class AppointPendingListVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型：1展商 2观众
     */
    private Integer businessType;
    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     * 预约方名称
     */
    private String memberName;
    /**
     * 预约目的（展商/观众）
     */
    private Integer purposeType;
    /**
     * 预约目的名称（展商/观众）
     */
    private String purposeName;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝 40.已取消
     */
    private Integer status;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 预约时间列表
     */
    private List<ExpoAppointmentTimeVO> timeList;
}
