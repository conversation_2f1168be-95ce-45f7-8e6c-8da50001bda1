package com.echronos.expo.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.TimeSerializerUtils;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 观众签到打印列表
 * <AUTHOR>
 * @date 2025-08-25 14:20
 */
@Data
public class AudiencePrintSignVO {

    /**
     * ID
     */
    private Integer id;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 最近签到时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime lastSignTime;
    /**
     * 扫码签到签到人
     */
    private String lastSignMemberName;
    /**
     * 签到次数
     */
    private Integer signCount;
    /**
     * 打印次数
     */
    private Integer qrCodePrintCount;

}
