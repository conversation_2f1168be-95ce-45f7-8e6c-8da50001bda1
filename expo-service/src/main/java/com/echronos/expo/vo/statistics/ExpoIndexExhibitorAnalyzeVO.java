package com.echronos.expo.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 展商分析报表（首页）
 * <AUTHOR>
 * @date 2025-08-20 15:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpoIndexExhibitorAnalyzeVO {

    /**
     * 总参展商
     */
    private BigDecimal total;
    /**
     * 新增参展商
     */
    private BigDecimal firstTotal;
    /**
     * 复展商
     */
    private BigDecimal repeatTotal;
    /**
     * 复参率
     */
    private BigDecimal repeatRete;

}
