package com.echronos.expo.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 展会展商参展频率
 * <AUTHOR>
 * @date 2025-08-20 21:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpoIndexExhibitorAnalyzeFrequencyVO {

    /**
     * 首次参展次数
     */
    private BigDecimal oneFrequency;
    /**
     * 首次参展占比
     */
    private BigDecimal oneFrequencyRate;
    /**
     * 参展2次次数
     */
    private BigDecimal twoFrequency;
    /**
     * 参展2次占比
     */
    private BigDecimal twoFrequencyRate;
    /**
     * 参展3次次数
     */
    private BigDecimal threeFrequency;
    /**
     * 参展3次占比
     */
    private BigDecimal threeFrequencyRate;
    /**
     * 参展3次以上次数
     */
    private BigDecimal fourFrequency;
    /**
     * 参展3次以上占比
     */
    private BigDecimal fourFrequencyRate;

}
