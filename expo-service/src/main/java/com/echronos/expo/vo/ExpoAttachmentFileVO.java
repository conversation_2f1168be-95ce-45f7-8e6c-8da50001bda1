/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.FilePathSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoAttachmentFile controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoAttachmentFileVO{


    /**
     *  主键 
     */
    private Integer id;
    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  业务id 
     */
    private Integer businessId;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     *  文件名称 
     */
    private String fileName;
    /**
     *  文件大小 
     */
    private BigDecimal fileSize;
    /**
     *  附件地址 
     */
    @JSONField(serializeUsing = FilePathSerializer.class)
    private String filePath;
    /**
     *  展会附件类型：1.展会手册  2.展会海报模板  3.展位订单  4.展位布局图
     */
    private Integer type;
    /**
     * 订单文件类型：0.图片  1.文件
     */
    private Integer orderFileType;

}