/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoEmailRuleConfig controller层返回值
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class ExpoEmailRuleConfigVO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 触发事件ID
     */
    private Integer eventId;
    /**
     * 触发事件名称
     */
    private String eventName;
    /**
     * 模板ID
     */
    private Integer templateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 规则类型：0-观众自动发送规则，1-展商自动发送规则
     */
    private Integer ruleType;
    /**
     * 触发天数（提前或延后的天数）
     */
    private Integer triggerDay;
    /**
     * 发送时间（格式：HH:mm:ss）
     */
    private LocalDateTime sendTime;
    /**
     * SMTP配置ID
     */
    private Integer smtpConfigId;
    /**
     * SMTP配置邮箱
     */
    private String smtpConfigEmail;
    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 收件观众角色：1普通观众 2专业买家 3全部观众
     */
    private Integer audienceRole;
    /**
     * 收件观众角色名称
     */
    private String audienceRoleName;
}