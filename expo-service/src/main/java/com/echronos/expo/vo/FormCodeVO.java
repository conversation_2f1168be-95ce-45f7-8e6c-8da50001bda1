package com.echronos.expo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-19 17:09
 */
@Data
public class FormCodeVO {

    /**
     * ID
     */
    private Integer id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 表单类型：1、观众注册表单，2、参展申请表单
     */
    private Integer formType;
    /**
     * 表单名称
     */
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 表单图片
     */
    private String formImageUrl;
    /**
     * 发布站点id
     */
    private String publishTenantId;
    /**
     * 自定义表单系统code
     */
    private String formCode;
    /**
     * 自定义表单版本号
     */
    private Long versionNumber;

}
