package com.echronos.expo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 观众二维码信息
 * <AUTHOR>
 * @date 2025-08-06 14:15
 */
@Data
public class ExpoAudienceQrCodeInfoVO {

    /**
     * 二维码图片
     */
    private String qrcodeImage;
    /**
     * 签到二维码
     */
    private String qrcodeUrl;
    /**
     * 展会名称
     */
    private String expoName;
    /**
     * 展馆名称
     */
    private String hallName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 主办方公司名称
     */
    private String organizerCompanyName;

}
