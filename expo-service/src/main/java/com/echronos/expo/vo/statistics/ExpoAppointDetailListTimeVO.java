package com.echronos.expo.vo.statistics;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/8/20 14:48
 */
@Data
public class ExpoAppointDetailListTimeVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 预约ID
     */
    private Integer appointmentId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝 40.已取消
     */
    private Integer status;
    /**
     * 状态名称
     */
    private String statusName;
}
