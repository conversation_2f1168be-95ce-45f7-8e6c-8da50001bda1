package com.echronos.expo.vo.exhibitor;

import lombok.Data;

import java.util.List;

/**
 * 展商信息
 * <AUTHOR>
 * @date 2025-08-07 14:14
 */
@Data
public class ExhibitorDetailVO {

    /**
     * 展商id
     */
    private Integer id;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 展商展位订单
     */
    private List<ExhibitorBoothOrderDetailVO> boothOrderList;
    /**
     * 展商租赁订单
     */
    private List<ExhibitorBoothLeaseDetailVO> leaseOrderList;
    /**
     * 展位号集合
     */
    private List<String> boothNumbersList;
    /**
     * 展商表单
     */
    private List<ExhibitorFormVO> exhibitorFormList;
    /**
     * 审核中数量
     */
    private Integer waitAuditCount = 0;
    /**
     * 审核通过数量
     */
    private Integer auditPassCount = 0;
    /**
     * 已拒绝数量
     */
    private Integer auditRejectCount = 0;
    /**
     * 待提交数量
     */
    private Integer toBeSubmitCount = 0;


}
