package com.echronos.expo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/8/25 11:16
 */
@Data
public class ExpoInteractionReportVO {
    /**
     * 总互动次数
     */
    private Integer totalInteractionCount;
    /**
     * 扫码次数
     */
    private Integer scanCount;
    /**
     * 预约次数
     */
    private Integer appointmentCount;
    /**
     * 名片交换次数
     */
    private Integer cardExchangeCount;
    /**
     * 扫码互动比率
     */
    private BigDecimal scanRate;
    /**
     * 预约会面比率
     */
    private BigDecimal appointmentRate;
    /**
     * 名片交换比率
     */
    private BigDecimal cardExchangeRate;
    /**
     * 总互动次数较上月同期百分比
     */
    private BigDecimal audienceDifferenceRate;
}
