/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ExpoReferenceForm controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
public class ExpoReferenceFormVO{

    /**
     *  主键id 
     */
    private Integer id;
    /**
     *  表单分组类型：1.观众相关表单  2.展商相关表单 
     */
    private Integer formGroup;
    /**
     *  表单类型：1.观众注册表单，2.观众满意度调查 20.企业信息收集  21.会刊收集  22.满意度调查  23.其它 
     */
    private Integer formType;
    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  业务ID：观众id/展商id 
     */
    private Integer businessId;
    /**
     *  自定义表单编码 
     */
    private String formCode;
    /**
     *  自定义表单版本 
     */
    private String formVersion;
    /**
     *  提交时间 
     */
    private LocalDateTime submitTime;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    /**
     *  审核状态：0.待审核 1.审核拒绝 2.审核通过 
     */
    private Integer auditStatus;
    /**
     *  审核备注 
     */
    private String auditRemark;
    /**
     *  表单填写的值
     */
    private List<ExpoReferenceFormExtendVO> extendList;

}