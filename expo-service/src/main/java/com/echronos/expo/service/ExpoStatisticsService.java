package com.echronos.expo.service;

import com.echronos.commons.Result;
import com.echronos.commons.page.PageVO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.vo.statistics.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-15 15:10
 */
public interface ExpoStatisticsService {

    /**
     * 展会总数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexExpoCount(Integer companyId);

    /**
     * 展会展位数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexExhibitorCount(Integer companyId);

    /**
     * 观众总数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexAudienceCount(Integer companyId);

    /**
     * 展位预订率（首页）
     * @return
     */
    ExpoIndexCountVO getIndexBoothCount(Integer companyId);

    /**
     * 展商分析报表（首页）
     * @param companyId
     * @return
     */
    ExpoIndexExhibitorAnalyzeVO getIndexExhibitorAnalyzeCount(Integer companyId);

    /**
     * 展商首次参展和复参展（首页-详情）
     * @param companyId
     * @return
     */
    ExpoIndexExhibitorAnalyzeFirstAndRepeatVO getIndexExhibitorAnalyzeFirstAndRepeatCount(Integer companyId);

    /**
     * 展商分析报表频次（首页-详情）
     * @param companyId
     * @return
     */
    ExpoIndexExhibitorAnalyzeFrequencyVO getIndexExhibitorAnalyzeFrequencyCount(Integer companyId);


    /**
     * 展商分析报表详情（首页-详情）
     * @param dto
     * @return
     */
    Result<List<ExpoIndexExhibitorAnalyzeDetailVO>> getIndexExhibitorAnalyzeDetailList(ExpoExhibitorDTO dto);

    /**
     * 展会观众维度统计
     * @param expoId
     * @return
     */
    ExpoAudienceStatisticsVO getExpoAudienceStatistics(Integer expoId);

    /**
     * 获取展会观众展位签到未签比率
     * @param expoId
     * @return
     */
    ExpoAudienceSignNotSignInRateVO getExpoAudienceSignInNotSignInCount(Integer expoId);

    /**
     * 获取展会观众渠道统计
     * @param expoId
     * @return
     */
    List<ExpoAudienceChannelVO> getExpoAudienceChannelList(Integer expoId);

    /**
     * 获取观众每天签到统计
     * @return
     */
    List<ExpoAudienceDateCountVO> getAudienceSingInDayCount(Integer expoId);

    /**
     * 获取展会观众签到时间段统计
     * @param expoId
     * @param time
     * @return
     */
    List<ExpoAudienceDateCountVO> getAudienceSingInTimeCount(Integer expoId, LocalDateTime time);
}
