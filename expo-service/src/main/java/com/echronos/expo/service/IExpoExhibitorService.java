package com.echronos.expo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.expo.dto.*;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.exhibitor.*;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * <AUTHOR>
 * date2025/8/4 15:09
 */
public interface IExpoExhibitorService {

    /**
     * 展商列表
     *
     * @param dto
     * @return
     */
    IPage<ExpoExhibitorVO> pageList(ExpoExhibitorDTO dto);

    /**
     * 编辑获取详情
     */
    ExpoExhibitorEditDetailVO getEditDetail(ExpoExhibitorDTO dto);

    /**
     * 编辑详情
     *
     * @param dto
     */
    void editDetail(ExpoExhibitorDTO dto);

    /**
     * 展商详情（平台方视角）
     * @param dto
     * @return
     */
    ExhibitorDetailPlatformVO getDetailPlatform(ExpoExhibitorDTO dto);

    /**
     * 展商详情（参展方视角）
     * @param dto
     * @return
     */
    ExhibitorDetailVO getDetailParticipants(ExpoExhibitorDTO dto);

    /**
     * 删除展商
     *
     * @param dto
     */
    void delete(ExpoExhibitorDTO dto);

    /**
     * 获取展商邀请二维码
     *
     * @param dto
     * @return
     */
    ExhibitorInviteQrCodeVO getInviteQrCode(ExpoExhibitorDTO dto);

    /**
     * 获取展商下拉列表
     * @param dto
     * @return
     */
    List<ExhibitorSelectVO> getSelectList(ExpoExhibitorDTO dto);

    /**
     * 添加租赁订单
     * @param dto
     */
    void addLeaseOrder(AddExhibitorOrderDTO dto);

    /**
     * 添加商旅订单
     * @param dto
     */
    void addTravelOrder(AddExhibitorOrderDTO dto);

    /**
     * 发送邮件
     * @param dto
     */
    void sendEmail(ExpoExhibitorEmailRecordsDTO dto);

    /**
     * 展商扫码
     *
     * @param dto
     */
    ExpoScanAudienceInfoVO scanCode(ExpoExhibitorDTO dto);

    /**
     * 建站查询展商信息
     *
     * @param dto
     */
    ExpoPageVO<WebExpoExhibitorVO> webQueryExhibitorList(ExpoExhibitorDTO dto);

    /**
     * 校验是否为展商对应展会的观众
     *
     * @param dto
     */
    ValidityHasAudienceVO validityHasAudience(ExpoExhibitorDTO dto);

    /**
     * 预约设置
     *
     * @param dto 参数
     */
    void appointSet(ExpoExhibitorDTO dto);

    /**
     * 查询总展商数量
     *
     * @param dto
     */
    ExhibitorNumberVO queryExhibitorTotal(ExpoExhibitorDTO dto);

    /**
     * 展商企业信息收集状态统计
     *
     * @param dto
     */
    ExpoExhibitorOtherInfoStatusVO expoExhibitorEnterpriseInfoStatus(ExpoExhibitorDTO dto);

    /**
     * 展商会刊信息收集状态统计
     *
     * @param dto
     * @return
     */
    ExpoExhibitorOtherInfoStatusVO expoExhibitorJournalInfoStatus(ExpoExhibitorDTO dto);

    /**
     * 官网展商展示
     * @param dto
     */
    ExpoPageVO<WebExpoExhibitorVO> queryExhibitorListByTenantId(ExpoExhibitorDTO dto);
}
