package com.echronos.expo.service;

import com.echronos.commons.page.PageVO;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.statistics.ExpoAppointDetailCountVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailListVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 展会互动统计服务接口
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
public interface IExpoInteractionStatisticsService {

    /**
     * 展会互动次数统计数据
     *
     * @param dto 统计参数
     * @return 互动次数统计结果
     */
    ExpoInteractionStatisticsVO interactionStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 预约状态分布统计
     *
     * @param dto 统计参数
     * @return 预约状态分布结果
     */
    ExpoAppointStatusVO appointmentStatusStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 每日互动趋势统计
     *
     * @param dto 统计参数
     * @return 每日互动趋势结果
     */
    List<ExpoInteractionTrendVO> interactionTrendStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 获取展会日期列表
     *
     * @param dto 统计参数
     * @return 展会日期列表
     */
    List<LocalDate> getExpoDates(ExpoInteractionStatisticsDTO dto);

    /**
     * 每小时互动分布统计
     *
     * @param dto 统计参数
     * @return 每小时互动分布结果
     */
    List<ExpoHourlyInteractionVO> hourlyInteractionStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 互动展商排行统计
     *
     * @param dto 统计参数
     * @return 互动展商排行结果
     */
    ExpoPageVO<ExpoExhibitorInteractionRankingVO> exhibitorInteractionRankingStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 展商扫码详情总计
     *
     * @param dto 统计参数
     * @return 展商扫码详情统计数据
     */
    ExpoExhibitorScanCountVO exhibitorScanDetailTotal(ExpoInteractionStatisticsDTO dto);

    /**
     * 展商扫码记录列表
     *
     * @param dto 统计参数
     * @return 展商扫码记录列表
     */
    ExpoPageVO<ExpoExhibitorScanListVO> exhibitorScanRecordList(ExpoInteractionStatisticsDTO dto);

    /**
     * 展商扫码观众详情列表
     *
     * @param dto 统计参数
     * @return 展商扫码观众详情列表
     */
    ExpoPageVO<ExpoExhibitorScanAudienceVO> exhibitorScanAudienceList(ExpoInteractionStatisticsDTO dto);

    /**
     * 预约详情预约数统计
     *
     * @param dto 参数
     * @return ExpoAppointDetailCountVO
     */
    ExpoAppointDetailCountVO appointDetailCount(ExpoAppointmentDTO dto);

    /**
     * 预约详情预约列表
     *
     * @param dto 参数
     * @return List<ExpoAppointDetailListVO>
     */
    PageVO<ExpoAppointDetailListVO> appointDetailList(ExpoAppointmentDTO dto);

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return ExpoAppointDetailVO
     */
    ExpoAppointDetailVO appointDetail(ExpoAppointmentDTO dto);

    /**
     * 互动分析报表统计
     *
     * @param dto 统计参数
     * @return 互动分析报表数据
     */
    ExpoInteractionReportVO report(ExpoInteractionStatisticsDTO dto);
}
