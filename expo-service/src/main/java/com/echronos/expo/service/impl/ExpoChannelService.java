package com.echronos.expo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoChannelDTO;
import com.echronos.expo.dto.ExpoChannelPageDTO;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.ExpoChannelConfigManager;
import com.echronos.expo.manager.ExpoChannelManager;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ExpoChannel;
import com.echronos.expo.model.ExpoChannelConfig;
import com.echronos.expo.service.IExpoChannelService;
import com.echronos.expo.vo.ExpoChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 展会渠道管理
 *
 * <AUTHOR>
 * @Date 2025/5/15 14:05
 * @ClassName ExpoChannelService
 */
@Slf4j
@Service
public class ExpoChannelService implements IExpoChannelService {

    @Resource
    private ExpoChannelManager expoChannelManager;
    @Resource
    private ExpoChannelConfigManager expoChannelConfigManager;

    @Override
    public Result<List<ExpoChannelVO>> pageFor(ExpoChannelPageDTO dto) {
        Page<ExpoChannel> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        Page<ExpoChannel> channelPage = expoChannelManager.page(page, new LambdaQueryWrapper<ExpoChannel>()
                .like(StringUtils.isNotBlank(dto.getKeywords()), ExpoChannel::getChannelName, dto.getKeywords())
                .eq(ExpoChannel::getChannelType, dto.getChannelType())
                .eq(ExpoChannel::getCompanyId, dto.getCompanyId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoChannelVO> vos = CopyObjectUtils.copyAlistToBlist(channelPage.getRecords(), ExpoChannelVO.class);
        return Result.build(vos, channelPage.getTotal());
    }

    @Override
    public Result<List<ExpoChannelVO>> channelList(ExpoChannelDTO dto) {
        List<ExpoChannel> channels = expoChannelManager.list(new LambdaQueryWrapper<ExpoChannel>()
                .like(StringUtils.isNotBlank(dto.getKeywords()), ExpoChannel::getChannelName, dto.getKeywords())
                .eq(ExpoChannel::getChannelType, dto.getChannelType())
                .eq(null != dto.getId(), ExpoChannel::getId, dto.getId())
                .eq(ExpoChannel::getCompanyId, dto.getCompanyId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoChannelVO> vos = CopyObjectUtils.copyAlistToBlist(channels, ExpoChannelVO.class);
        return Result.build(vos);
    }

    @Override
    public Result save(ExpoChannelDTO dto) {
        ExpoChannel expoChannel = expoChannelManager.getOne(new LambdaQueryWrapper<ExpoChannel>()
                .eq(ExpoChannel::getChannelName, dto.getChannelName())
                .eq(ExpoChannel::getCompanyId, dto.getCompanyId())
        );
        if (Objects.nonNull(expoChannel)) {
            if (Objects.isNull(dto.getId()) || (Objects.nonNull(dto.getId()) && expoChannel.getId() != dto.getId())) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_EXIST.getMessage());
            }
        }
        if (Objects.nonNull(dto.getId())) {
            ExpoChannel channel = expoChannelManager.getOne(new LambdaQueryWrapper<ExpoChannel>()
                    .eq(ExpoChannel::getId, dto.getId())
                    .eq(ExpoChannel::getCompanyId, dto.getCompanyId())
            );
            if (Objects.isNull(channel)) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getMessage());
            }
        }
        expoChannelManager.saveOrUpdate(dto);
        return Result.build();
    }

    @Override
    public Result del(ExpoChannelDTO dto) {
        ExpoChannel expoChannel = expoChannelManager.getOne(new LambdaQueryWrapper<ExpoChannel>()
                .eq(ExpoChannel::getCompanyId, dto.getCompanyId())
                .eq(ExpoChannel::getId, dto.getId())
        );
        if (Objects.isNull(expoChannel)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getMessage());
        }
        List<ExpoChannelConfig> channelConfigs = expoChannelConfigManager.list(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getChannelId, expoChannel.getId())
        );
        if (CollectionUtils.isNotEmpty(channelConfigs)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_CANT_DEL.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_CANT_DEL.getMessage());
        }
        expoChannelManager.removeById(expoChannel.getId());
        return Result.build();
    }
}
