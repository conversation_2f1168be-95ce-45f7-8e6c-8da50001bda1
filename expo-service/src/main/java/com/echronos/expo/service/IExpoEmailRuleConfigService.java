package com.echronos.expo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoEmailRuleConfigDTO;
import com.echronos.expo.vo.ExpoEmailRuleConfigVO;

/**
 * <AUTHOR>
 * @date 2025/8/26 15:01
 */
public interface IExpoEmailRuleConfigService {

    /**
     * 新增邮件自动发送规则
     *
     * @param dto 参数
     */
    void add(ExpoEmailRuleConfigDTO dto);

    /**
     * 删除邮件自动发送规则
     *
     * @param dto 参数
     */
    void del(ExpoEmailRuleConfigDTO dto);

    /**
     * 修改邮件自动发送规则
     *
     * @param dto 参数
     */
    void update(ExpoEmailRuleConfigDTO dto);

    /**
     * 获取邮件自动发送规则详情
     *
     * @param dto 参数
     * @return ExpoEmailRuleConfigVO
     */
    ExpoEmailRuleConfigVO detail(ExpoEmailRuleConfigDTO dto);

    /**
     * 获取邮件自动发送规则配置分页列表
     *
     * @param dto 参数
     * @return Page<ExpoEmailRuleConfigVO>
     */
    Page<ExpoEmailRuleConfigVO> pageList(ExpoEmailRuleConfigDTO dto);

    /**
     * 修改邮件自动发送规则状态（启用，禁用）
     *
     * @param dto 参数
     */
    void updateStatus(ExpoEmailRuleConfigDTO dto);
}
