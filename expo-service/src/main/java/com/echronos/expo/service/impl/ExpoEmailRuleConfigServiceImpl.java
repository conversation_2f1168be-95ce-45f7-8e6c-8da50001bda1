package com.echronos.expo.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoEmailRuleConfigDTO;
import com.echronos.expo.enums.ExpoAudienceRoleEnum;
import com.echronos.expo.enums.ExpoEmailSendEventEnum;
import com.echronos.expo.enums.TriggerTypeEnum;
import com.echronos.expo.manager.ExpoEmailRuleConfigManager;
import com.echronos.expo.manager.FeignCommonManager;
import com.echronos.expo.model.ExpoEmailRuleConfig;
import com.echronos.expo.service.IExpoEmailRuleConfigService;
import com.echronos.expo.vo.ExpoEmailRuleConfigVO;
import com.echronos.nms.api.resp.EmailSmtpConfigInfoResp;
import com.echronos.nms.api.resp.EmailTemplateInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/26 15:01
 */
@Slf4j
@Service
public class ExpoEmailRuleConfigServiceImpl implements IExpoEmailRuleConfigService {

    @Resource
    private ExpoEmailRuleConfigManager expoEmailRuleConfigManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    /**
     * 新增邮件自动发送规则
     *
     * @param dto 参数
     */
    @Override
    public void add(ExpoEmailRuleConfigDTO dto) {
        // 校验
        check(dto);
        // 新增
        ExpoEmailRuleConfig newEmailRuleConfig = CopyObjectUtils.copyAtoB(dto, ExpoEmailRuleConfig.class);
        expoEmailRuleConfigManager.save(newEmailRuleConfig);
    }

    /**
     * 删除邮件自动发送规则
     *
     * @param dto 参数
     */
    @Override
    public void del(ExpoEmailRuleConfigDTO dto) {
        ExpoEmailRuleConfig emailRuleConfig = expoEmailRuleConfigManager.queryOne(dto);
        if (Objects.isNull(emailRuleConfig)) {
            throw new BusinessException(-1, "发送规则不存在");
        }
        expoEmailRuleConfigManager.delById(dto.getId(), dto.getUpdateUser());
    }

    /**
     * 修改邮件自动发送规则
     *
     * @param dto 参数
     */
    @Override
    public void update(ExpoEmailRuleConfigDTO dto) {
        // 校验
        check(dto);
        // 更新
        ExpoEmailRuleConfig newEmailRuleConfig = CopyObjectUtils.copyAtoB(dto, ExpoEmailRuleConfig.class);
        expoEmailRuleConfigManager.updateById(newEmailRuleConfig);
    }

    /**
     * 校验
     */
    private void check(ExpoEmailRuleConfigDTO dto) {
        if (null != dto.getId()) {
            ExpoEmailRuleConfig emailRuleConfig = expoEmailRuleConfigManager.queryOne(dto);
            if (Objects.isNull(emailRuleConfig)) {
                throw new BusinessException(-1, "发送规则不存在");
            }
        }
        ExpoEmailSendEventEnum eventEnum = ExpoEmailSendEventEnum.getByCode(dto.getEventId());
        if (Objects.isNull(eventEnum)) {
            throw new BusinessException(-1, "触发事件不存在");
        }
        if (!eventEnum.getTriggerType().getType().equals(TriggerTypeEnum.IMMEDIATE.getType())) {
            if (dto.getTriggerDay() == null || dto.getTriggerDay() < 1) {
                throw new BusinessException(-1, "天数不能为空");
            }
            if (dto.getSendTime() == null) {
                throw new BusinessException(-1, "发送时间不能为空");
            }
        }
        if (eventEnum.getCode().equals(ExpoEmailSendEventEnum.AUDIENCE_REGISTER.getCode())) {
            if (dto.getAudienceRole() == null) {
                throw new BusinessException(-1, "请选择收件观众角色");
            }
        }
        // 校验名称是否重复
        ExpoEmailRuleConfigDTO queryDTO = new ExpoEmailRuleConfigDTO();
        queryDTO.setExpoId(dto.getExpoId());
        queryDTO.setRuleName(dto.getRuleName());
        queryDTO.setIgnoreId(dto.getId());
        ExpoEmailRuleConfig ruleConfig = expoEmailRuleConfigManager.queryBy(queryDTO);
        if (Objects.nonNull(ruleConfig)) {
            throw new BusinessException(-1, "已经存在同名的自动发送规则");
        }
        // 成为展商事件只能有一个
        if (eventEnum.getCode().equals(ExpoEmailSendEventEnum.BECOME_EXHIBITOR.getCode())) {
            ExpoEmailRuleConfigDTO query = new ExpoEmailRuleConfigDTO();
            query.setExpoId(dto.getExpoId());
            query.setEventId(dto.getEventId());
            query.setIgnoreId(dto.getId());
            ExpoEmailRuleConfig expoEmailRuleConfig = expoEmailRuleConfigManager.queryBy(query);
            if (Objects.nonNull(expoEmailRuleConfig)) {
                throw new BusinessException(-1, "已经配置了该触发事件的自动发送规则");
            }
        }
        // 观众注册事件校验
        if (eventEnum.getCode().equals(ExpoEmailSendEventEnum.AUDIENCE_REGISTER.getCode())) {
            ExpoEmailRuleConfigDTO query = new ExpoEmailRuleConfigDTO();
            query.setExpoId(dto.getExpoId());
            query.setEventId(dto.getEventId());
            query.setIgnoreId(dto.getId());
            List<ExpoEmailRuleConfig> emailRuleConfigs = expoEmailRuleConfigManager.queryList(query);
            if (!CollectionUtils.isEmpty(emailRuleConfigs)) {
                boolean b = emailRuleConfigs.stream().anyMatch(v -> v.getAudienceRole().equals(dto.getAudienceRole()));
                if (b) {
                    throw new BusinessException(-1, "已经配置了该触发事件的自动发送规则");
                } else {
                    if (emailRuleConfigs.size() == 1) {
                        if (emailRuleConfigs.get(0).getAudienceRole().equals(ExpoAudienceRoleEnum.ALL_AUDIENCE.getCode())) {
                            throw new BusinessException(-1, "已经配置了该触发事件的自动发送规则");
                        }
                    } else {
                        throw new BusinessException(-1, "已经配置了该触发事件的自动发送规则");
                    }
                }
            }
        }
        // 检查配置的模板id是否存在该模板
        EmailTemplateInfoResp emailTemplateInfoResp = feignCommonManager.queryEmailTempById(dto.getTemplateId());
        if (Objects.isNull(emailTemplateInfoResp)) {
            throw new BusinessException(-1, "邮件模板不存在");
        }
        // 检查配置的smtp配置是否存在
        EmailSmtpConfigInfoResp emailSmtpConfigInfoResp = feignCommonManager.queryEmailSmtpById(dto.getSmtpConfigId());
        if (Objects.isNull(emailSmtpConfigInfoResp)) {
            throw new BusinessException(-1, "邮件smtp配置不存在");
        }
    }

    /**
     * 获取邮件自动发送规则详情
     *
     * @param dto 参数
     * @return ExpoEmailRuleConfigVO
     */
    @Override
    public ExpoEmailRuleConfigVO detail(ExpoEmailRuleConfigDTO dto) {
        ExpoEmailRuleConfig emailRuleConfig = expoEmailRuleConfigManager.queryOne(dto);
        if (Objects.nonNull(emailRuleConfig)) {
            ExpoEmailRuleConfigVO vo = CopyObjectUtils.copyAtoB(emailRuleConfig, ExpoEmailRuleConfigVO.class);
            // 事件名称
            ExpoEmailSendEventEnum eventEnum = ExpoEmailSendEventEnum.getByCode(emailRuleConfig.getEventId());
            if (Objects.nonNull(eventEnum)) {
                vo.setEventName(eventEnum.getName());
            }
            // 收件观众角色名称
            ExpoAudienceRoleEnum audienceRoleEnum = ExpoAudienceRoleEnum.getByCode(emailRuleConfig.getAudienceRole());
            if (Objects.nonNull(audienceRoleEnum)) {
                vo.setAudienceRoleName(audienceRoleEnum.getName());
            }
            // 邮件模板名称
            EmailTemplateInfoResp emailTemplateInfoResp = feignCommonManager.queryEmailTempById(emailRuleConfig.getTemplateId());
            if (Objects.nonNull(emailTemplateInfoResp)) {
                vo.setTemplateName(emailTemplateInfoResp.getName());
            }
            // 发送邮箱
            EmailSmtpConfigInfoResp emailSmtpConfigInfoResp = feignCommonManager.queryEmailSmtpById(emailRuleConfig.getSmtpConfigId());
            vo.setSmtpConfigEmail(emailSmtpConfigInfoResp.getUsername());
            return vo;
        }
        return null;
    }

    /**
     * 获取邮件自动发送规则配置分页列表
     *
     * @param dto 参数
     * @return Page<ExpoEmailRuleConfigVO>
     */
    @Override
    public Page<ExpoEmailRuleConfigVO> pageList(ExpoEmailRuleConfigDTO dto) {
        Page<ExpoEmailRuleConfig> page = expoEmailRuleConfigManager.pageList(dto);
        Page<ExpoEmailRuleConfigVO> voPage = new Page<>();
        voPage.setTotal(page.getTotal());
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<ExpoEmailRuleConfigVO> emailRuleConfigVOS = CopyObjectUtils.copyAlistToBlist(page.getRecords(), ExpoEmailRuleConfigVO.class);
            // 查询邮件模板
            List<Integer> tempIds = emailRuleConfigVOS.stream().map(ExpoEmailRuleConfigVO::getTemplateId).distinct().collect(Collectors.toList());
            List<EmailTemplateInfoResp> tempList = feignCommonManager.batchQueryEmailTempById(tempIds);
            Map<Integer, EmailTemplateInfoResp> tempMap = tempList.stream().collect(Collectors.toMap(EmailTemplateInfoResp::getId, v -> v));
            // 查询SMTP配置
            List<Integer> smtpIds = emailRuleConfigVOS.stream().map(ExpoEmailRuleConfigVO::getSmtpConfigId).distinct().collect(Collectors.toList());
            List<EmailSmtpConfigInfoResp> smtpList = feignCommonManager.batchQueryEmailSmtpById(smtpIds);
            Map<Integer, EmailSmtpConfigInfoResp> smtpMap = smtpList.stream().collect(Collectors.toMap(EmailSmtpConfigInfoResp::getId, v -> v));

            // 组装数据
            for (ExpoEmailRuleConfigVO emailRuleConfigVO : emailRuleConfigVOS) {
                // 事件名称
                ExpoEmailSendEventEnum eventEnum = ExpoEmailSendEventEnum.getByCode(emailRuleConfigVO.getEventId());
                if (Objects.nonNull(eventEnum)) {
                    String enumName = eventEnum.getName();
                    if (!eventEnum.getTriggerType().getType().equals(TriggerTypeEnum.IMMEDIATE.getType())) {
                        String newName = enumName.substring(0, enumName.length() - 3);
                        emailRuleConfigVO.setEventName(newName + emailRuleConfigVO.getTriggerDay() + "天 " + emailRuleConfigVO.getSendTime());
                    } else {
                        emailRuleConfigVO.setEventName(enumName);
                    }
                }
                // 模板名称
                emailRuleConfigVO.setTemplateName(tempMap.getOrDefault(emailRuleConfigVO.getTemplateId(), new EmailTemplateInfoResp()).getName());
                // 邮箱
                emailRuleConfigVO.setSmtpConfigEmail(smtpMap.getOrDefault(emailRuleConfigVO.getSmtpConfigId(), new EmailSmtpConfigInfoResp()).getUsername());
            }
        }
        return voPage;
    }

    /**
     * 修改邮件自动发送规则状态（启用，禁用）
     *
     * @param dto 参数
     */
    @Override
    public void updateStatus(ExpoEmailRuleConfigDTO dto) {
        ExpoEmailRuleConfig emailRuleConfig = expoEmailRuleConfigManager.queryOne(dto);
        if (Objects.isNull(emailRuleConfig)) {
            throw new BusinessException(-1, "发送规则不存在");
        }
        ExpoEmailRuleConfig update = new ExpoEmailRuleConfig();
        update.setId(emailRuleConfig.getId());
        update.setStatus(dto.getStatus());
        // 更新状态
        expoEmailRuleConfigManager.updateById(update);
    }
}
