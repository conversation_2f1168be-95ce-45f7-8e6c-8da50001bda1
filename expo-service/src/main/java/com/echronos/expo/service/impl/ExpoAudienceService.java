package com.echronos.expo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.crm.feign.ICustomerAudienceClient;
import com.echronos.crm.req.AddCustomerAudienceReq;
import com.echronos.crm.resp.CustomerAudienceResp;
import com.echronos.expo.constants.ExpoAudienceConstants;
import com.echronos.expo.constants.ExpoAudienceQueryConstants;
import com.echronos.expo.constants.GatewayRoutingConstants;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.*;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.model.ext.ExpoAudienceExt;
import com.echronos.expo.param.*;
import com.echronos.expo.service.IExpoAudienceService;
import com.echronos.expo.util.*;
import com.echronos.expo.util.excel.*;
import com.echronos.expo.vo.*;
import com.echronos.iform.api.entity.CollectFilter;
import com.echronos.iform.api.resp.ComponentResp;
import com.echronos.iform.api.resp.FormResp;
import com.echronos.iform.sdk.utils.SqlUtils;
import com.echronos.nms.api.enums.EmailAutoSendEventEnum;
import com.echronos.nms.api.enums.ServiceNameEnum;
import com.echronos.nms.api.feign.IEMailFeign;
import com.echronos.nms.api.req.SendEmailReq;
import com.echronos.nms.api.resp.EmailTemplateResp;
import com.echronos.tenant.api.resp.TenantInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 观众管理
 *
 * <AUTHOR>
 * @Date 2025/5/16 11:03
 * @ClassName ExpoAudienceService
 */
@Slf4j
@Service
public class ExpoAudienceService implements IExpoAudienceService {
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoAudienceExtendManager expoAudienceExtendManager;
    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoChannelManager expoChannelManager;
    @Resource
    private ExpoChannelConfigManager expoChannelConfigManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private ExpoFormFieldManager formFieldManager;
    @Resource
    private ExpoAudienceSignManager expoAudienceSignManager;
    @Resource
    private ExpoAudienceEmailRecordsManager expoAudienceEmailRecordsManager;
    @Resource
    private ICustomerAudienceClient customerAudienceClient;

    @Resource
    private IEMailFeign ieMailFeign;

    @Value("${remote.h5.domain}")
    private String gateway;

    @Resource
    private ExpoAppointedPersonnelManager expoAppointedPersonnelManager;

    @Resource
    private ExpoReferenceFormManager expoReferenceFormManager;
    @Resource
    private ExpoReferenceFormExtendManager expoReferenceFormExtendManager;
    @Resource
    private FeignCommonManager feignCommonManager;


    @Override
    public Result<List<ExpoAudienceVO>> pageFor(ExpoAudienceListFilterParam param) {
        Page<ExpoAudienceDTO> page = new Page<>(param.getPageNo(), param.getPageSize());
        ExpoAudiencePageDTO dto = new ExpoAudiencePageDTO();
        dto.setExpoId(param.getExpoId());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setKeywords(param.getKeywords());
        Set<String> fieldList = expoAudienceExtendManager.fieldList(param.getExpoId(), null);
        dto.setFieldList(Lists.newArrayList(fieldList));
        List<CollectFilterParam> filters = param.getFilters();
        String asBuyer = ExpoAudienceQueryConstants.asBuyer;
        List<String> selectList = Lists.newArrayList(FormFieldEnums.SELECT.getType(),
                FormFieldEnums.RADIO.getType(),
                FormFieldEnums.CHECKBOX.getType()
        );
        if (CollectionUtils.isNotEmpty(filters)) {
            List<CollectConditionParam> conditions = filters.stream().map(CollectFilterParam::getConditions)
                    .flatMap(List::stream).collect(Collectors.toList());
            CollectConditionParam condition = conditions.stream().filter(r -> r.getColName().equals(asBuyer))
                    .findFirst().orElse(null);
            if (Objects.nonNull(condition) && condition.getValue().size() > 0) {
                dto.setBuyerList(condition.getValue().stream().map(r -> Objects.nonNull(r) ? r.toString() : null).collect(Collectors.toList()));
            }
            //自定义表单下拉框
            List<CollectConditionParam> filterConditions = conditions.stream().filter(r -> selectList.contains(r.getComponentType()) &&
                    !r.getColName().equals(asBuyer)).collect(Collectors.toList());
            filters.stream().forEach(r -> {
                List<CollectConditionParam> collect = r.getConditions().stream().filter(s -> !selectList.contains(s.getComponentType()))
                        .collect(Collectors.toList());
                r.setConditions(collect);
            });
            List<CollectFilter> collectFilters = CopyObjectUtils.copyAlistToBlist(filters, CollectFilter.class);
            StringBuilder stringBuilder = new StringBuilder();
            filterConditions.stream().forEach(r -> {
                stringBuilder.append(" and ");
                if (CollectionUtils.isNotEmpty(r.getValue()) && r.getValue().size() > 0) {
                    stringBuilder.append(" case when JSON_VALID(" + r.getColName() + ") = 0 then " + r.getColName() + " in (");
                    StringBuilder value1 = new StringBuilder("");
                    for (int i = 0; i < r.getValue().size(); i++) {
                        value1.append("'").append(r.getValue().get(i)).append("'");
                        if (i != r.getValue().size() - 1) {
                            value1.append(",");
                        }
                    }
                    stringBuilder.append(value1 + ")");
                    stringBuilder.append("when JSON_VALID(" + r.getColName() + ") = 1 then JSON_CONTAINS(" + r.getColName() +
                            ", JSON_ARRAY(" + value1 + "))"
                    );
                    stringBuilder.append("else false end ");
                }
            });
            String buildConditionNew = SqlUtils.buildConditionNew(collectFilters, "expo_audience_field", null, dto.getCompanyId());
            if (StringUtils.isNotBlank(buildConditionNew)) {
                stringBuilder.append(" and ");
            }
            stringBuilder.append(buildConditionNew);
            if (StringUtils.isNotBlank(stringBuilder.toString())) {
                dto.setWhereSqlStr(stringBuilder.toString());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getSort()) && param.getSort().size() > 0) {
            dto.setSortSqlStr(SqlUtils.buildSortNew(param.getSort(), "expo_audience_field", null, dto.getCompanyId()));
        }
        //转换行转列值
        Page<ExpoAudienceDTO> audienceDTOPage = expoAudienceManager.pageForAudience(page, dto);
        List<ExpoAudienceDTO> records = audienceDTOPage.getRecords();
        Map<Integer, Map<String, ExpoAudienceExtend>> mapMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            Set<Integer> audienceIds = records.stream().map(ExpoAudience::getId).collect(Collectors.toSet());
            List<ExpoAudienceExtend> extendList = expoAudienceExtendManager.list(new LambdaQueryWrapper<ExpoAudienceExtend>()
                    .in(CollectionUtils.isNotEmpty(audienceIds), ExpoAudienceExtend::getAudienceId, audienceIds)
                    .eq(ExpoAudienceExtend::getExpoId, param.getExpoId())
            );
            mapMap.putAll(extendList.stream().collect(Collectors.
                    groupingBy(ExpoAudienceExtend::getAudienceId, Collectors.toMap(ExpoAudienceExtend::getColName, r -> r))));
        }
        List<ExpoAudienceVO> vos = CopyObjectUtils.copyAlistToBlist(records, ExpoAudienceVO.class);
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
        vos.stream().forEach(r -> {
            Map<String, String> paramMap = new HashMap<>();
            Map<String, ExpoAudienceExtend> listMap = mapMap.get(r.getId());
            if (CollectionUtils.isNotEmpty(listMap)) {
                listMap.entrySet().stream().forEach(s -> {
                    FormFieldDTO fieldDTO = fieldDTOMap.get(s.getKey());
                    if (Objects.isNull(fieldDTO)) {
                        return;
                    }
                    FormFieldEnums fieldEnums = FormFieldEnums.getByType(fieldDTO.getType());
                    switch (fieldEnums) {
                        case TEXT:
                            paramMap.put(s.getKey(), s.getValue().getColValue());
                            break;
                        case CHECKBOX:
                        case SELECT:
                        case RADIO:
                            try {
                                List<String> strings = JSONArray.parseArray(s.getValue().getColValue(), String.class);
                                if (CollectionUtils.isNotEmpty(strings)) {
                                    paramMap.put(s.getKey(), strings.stream().collect(Collectors.joining(",")));
                                }
                            } catch (Exception e) {
                                paramMap.put(s.getKey(), s.getValue().getColValue());
                            }
                            break;
                    }
                });
                r.setExtMap(paramMap);
            }
        });
        return Result.build(vos, audienceDTOPage.getTotal());
    }

    @Override
    public Result<List<ExpoAudienceVO>> pageList(ExpoAudiencePageDTO dto) {
        Page<ExpoAudienceDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        //查询行转列字段
        FormSqlFieldDTO formSqlFieldDTO = expoFormManager.getFromSqlFieldAndSort("", null, dto.getFilters(), dto.getSort());
        dto.setFieldList(formSqlFieldDTO.getRowToColumnSql());
        //筛选条件
        dto.setWhereSqlStr(formSqlFieldDTO.getConditionScreen());
        //排序条件
        dto.setSortStr(formSqlFieldDTO.getBuildSortNew());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        //转换行转列值
        Page<ExpoAudienceDTO> audienceDTOPage = expoAudienceManager.pageList(page, dto);
        if (CollectionUtils.isEmpty(audienceDTOPage.getRecords())){
           return Result.build(null, page.getTotal());
        }
        List<Integer> audienceIds = audienceDTOPage.getRecords().stream().map(ExpoAudience::getId).collect(Collectors.toList());
        List<ExpoAudienceVO> vos = CopyObjectUtils.copyAlistToBlist(audienceDTOPage.getRecords(), ExpoAudienceVO.class);

        // 查询观众表单
        List<ExpoReferenceForm> audienceFormList = expoReferenceFormManager.getListByBusinessIdAndType(audienceIds, ExpoFormEnums.FormGroup.AUDIENCE.getCode(),
                ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        Map<Integer, List<ExpoReferenceFormExtend>> allAudienceFormExtendMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(audienceFormList)){
            List<Integer> formIdList = audienceFormList.stream().map(audience -> audience.getId()).collect(Collectors.toList());
            // 查询观众表单值
            List<ExpoReferenceFormExtend> audienceFormExtendList = expoReferenceFormExtendManager.getListByRefFormIds(formIdList);
            if(CollectionUtils.isNotEmpty(audienceFormExtendList)){
                Map<Integer, List<ExpoReferenceFormExtend>> audienceFormExtendMap = audienceFormExtendList.stream().collect(Collectors.groupingBy(ExpoReferenceFormExtend::getReferenceFormId));
                for(ExpoReferenceForm audienceForm : audienceFormList){
                    if(audienceFormExtendMap.containsKey(audienceForm.getBusinessId())){
                        List<ExpoReferenceFormExtend> formExtendList = audienceFormExtendMap.get(audienceForm.getBusinessId());
                        allAudienceFormExtendMap.put(audienceForm.getBusinessId(), formExtendList);
                    }
                }
            }
        }

        vos.stream().forEach(r -> {
            Map<String, String> extMap = new HashMap<>();
            if(allAudienceFormExtendMap.containsKey(r.getId())){
                List<ExpoReferenceFormExtend> formExtendList = allAudienceFormExtendMap.get(r.getId());
                for(ExpoReferenceFormExtend formExtend : formExtendList){
                    extMap.put(formExtend.getColName(), formExtend.getColValue());
                }
            }
            r.setExtMap(extMap);
        });
        return Result.build(vos, audienceDTOPage.getTotal());
    }

    @Override
    public Result<List<AudiencePrintSignVO>> printSignPageList(ExpoAudiencePageDTO dto) {
        Page<ExpoAudience> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        //查询行转列字段
        FormSqlFieldDTO formSqlFieldDTO = expoFormManager.getFromSqlFieldAndSort("", null, dto.getFilters(), dto.getSort());
        //筛选条件
        dto.setWhereSqlStr(formSqlFieldDTO.getConditionScreen());
        //排序条件
        dto.setSortStr(formSqlFieldDTO.getBuildSortNew());
        List<ExpoAudienceDTO> recordList = expoAudienceManager.printSignPageList(dto, page);
        List<AudiencePrintSignVO> voList = null;
        if(CollectionUtils.isNotEmpty(recordList)){
            voList = CopyObjectUtils.copyAlistToBlist(recordList, AudiencePrintSignVO.class);
        }
        return Result.build(voList, page.getTotal());
    }

    @Override
    public void addPrintFrequency(ExpoAudienceDTO dto) {
        ExpoAudience audience = expoAudienceManager.queryById(dto.getId());
        if(null == audience){
            throw new BusinessException(-1, "观众不存在");
        }else if(audience.getCompanyId() != dto.getCompanyId()){
            throw new BusinessException(-1, "不能操作其他公司数据");
        }
        Integer qrCodePrintCount = null == audience.getQrCodePrintCount() ? 1 : (audience.getQrCodePrintCount() + 1);
        expoAudienceManager.updateQrCodePrintSignCount(audience.getId(), qrCodePrintCount);
    }

    @Override
    public List<ExpoAudienceDTO> list(ExpoAudienceListFilterExportParam param) {
        List<ExpoAudienceDTO> records = records(param, RequestUserUtils.getUser().getCompanyId());
        return records;
    }

    @Override
    public Result<ExpoAudienceVO> info(ExpoAudienceDTO dto) {
        ExpoAudience audience = expoAudienceManager.getOne(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudience::getId, dto.getId())
        );
        ExpoAudienceVO vo = CopyObjectUtils.copyAtoB(audience, ExpoAudienceVO.class);
        if (Objects.isNull(audience)) {
            return Result.build(vo);
        }
        ExpoReferenceForm expoReferenceForm = expoReferenceFormManager.getByGroupTypeBusinessId(audience.getId(),
                ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null != expoReferenceForm){
            Map<String, String> extMap = new HashMap<>();
            List<ExpoReferenceFormExtend> extendList = expoReferenceFormExtendManager.getListByRefFormId(expoReferenceForm.getId());
            extendList.stream().forEach(extend -> {
                extMap.put(extend.getColName(), extend.getColValue());
            });
            vo.setFormCode(expoReferenceForm.getFormCode());
            vo.setExtMap(extMap);
        }
        List<ExpoAudienceSign> signs = expoAudienceSignManager.list(new LambdaQueryWrapper<ExpoAudienceSign>()
                .eq(ExpoAudienceSign::getAudienceId, audience.getId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        vo.setIsSign("0");
        if (CollectionUtils.isNotEmpty(signs)) {
            vo.setIsSign("1");
            ExpoAudienceSign expoAudienceSign = signs.stream().findFirst().get();
            vo.setLastSignTime(expoAudienceSign.getCreateTime());
            vo.setSignCount(signs.size());
        }
        List<ExpoAudienceEmailRecords> emailRecords = expoAudienceEmailRecordsManager.list(new LambdaQueryWrapper<ExpoAudienceEmailRecords>()
                .eq(ExpoAudienceEmailRecords::getIsSend, CommonStatus.YesOrNoEnum.YES.getValue())
                .eq(ExpoAudienceEmailRecords::getAudienceId, audience.getId())
        );
        vo.setIsSend("0");
        if (CollectionUtils.isNotEmpty(emailRecords)) {
            vo.setIsSend("1");
            vo.setSendCount(emailRecords.size());
            vo.setLastSendTime(emailRecords.stream().findFirst().get().getCreateTime());
        }
        ExpoChannel channel = expoChannelManager.getById(audience.getChannelId());
        if (Objects.nonNull(channel)) {
            vo.setChannelName(channel.getChannelName());
        }
        return Result.build(vo);
    }

    @Override
    public Result<List<ExpoAudienceVO>> list(ExpoAudienceListDTO dto) {
        List<ExpoAudience> list = expoAudienceManager.list(new LambdaQueryWrapper<ExpoAudience>()
                .eq(Objects.nonNull(dto.getExpoId()), ExpoAudience::getExpoId, dto.getExpoId())
                .in(CollectionUtils.isNotEmpty(dto.getIdList()), ExpoAudience::getId, dto.getIdList())
                .and(StringUtils.isNotBlank(dto.getKeywords()), wra -> wra
                        .like(ExpoAudience::getEmail, dto.getKeywords())
                        .or()
                        .like(ExpoAudience::getName, dto.getKeywords())
                        .or()
                        .like(ExpoAudience::getPhone, dto.getPhone())
                        .or()
                        .like(ExpoAudience::getCompanyName, dto.getCompanyName())
                )
        );
        List<ExpoAudienceVO> vos = CopyObjectUtils.copyAlistToBlist(list, ExpoAudienceVO.class);
        return Result.build(vos);
    }

    @Override
    public Result<ExpoFormVO> scanRegisterForm(ExpoFormDTO dto) {
        ExpoChannelConfig config = expoChannelConfigManager.getOne(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getExpoId, dto.getExpoId())
                .eq(ExpoChannelConfig::getId, dto.getId())
        );
        ExpoForm expoForm = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                .eq(Objects.nonNull(dto.getExpoId()), ExpoForm::getExpoId, dto.getExpoId())
                .eq(ExpoForm::getId, config.getFormId())
        );
        if (Objects.isNull(expoForm)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        ExpoFormVO vo = CopyObjectUtils.copyAtoB(expoForm, ExpoFormVO.class);
        vo.setId(config.getId());
        vo.setFormFieldList(JSONObject.parseArray(expoForm.getFieldJson(), FormFieldVO.class));
        List<ExpoFormField> fieldList = formFieldManager.list(new LambdaQueryWrapper<ExpoFormField>()
                .eq(BaseEntity::getIsDeleted, CommonStatus.YesOrNoEnum.NO.getValue())
                .eq(ExpoFormField::getFormId, expoForm.getExpoId())
        );
        vo.setFieldList(fieldList.stream().map(ExpoFormField::getColName).collect(Collectors.toList()));
        return Result.build(vo);
    }

    @Override
    public Result<ExpoAudienceScanVO> register(ExpoAudienceDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
                .eq(BaseEntity::getId, dto.getExpoId())
        );
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getMessage());
        }
        ExpoChannelConfig config = expoChannelConfigManager.getOne(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getExpoId, dto.getExpoId())
                .eq(ExpoChannelConfig::getId, dto.getChannelId())
        );
        if (Objects.isNull(config)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_NOT_EXIST.getMessage());
        }
        ExpoAudience audience = expoAudienceManager.getOne(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getEmail, dto.getEmail())
                .eq(ExpoAudience::getExpoId, dto.getExpoId())
        );
        dto.setChannelId(config.getChannelId());
        String scanAudienceSignCode = GatewayRoutingConstants.scanAudienceSignCode();
        if (Objects.nonNull(audience)) {
            dto.setId(audience.getId());
            expoAudienceExtendManager.remove(new LambdaQueryWrapper<ExpoAudienceExtend>()
                    .eq(ExpoAudienceExtend::getAudienceId, audience.getId())
            );
        }
        expoAudienceManager.saveOrUpdate(dto);
        Map<String, String> extMap = dto.getExtMap();
        List<ExpoAudienceExtend> extendList = new ArrayList<>();
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
        extMap.entrySet().forEach(r -> {
            FormFieldDTO fieldDTO = fieldDTOMap.get(r.getKey());
            if (Objects.isNull(fieldDTO)) {
                return;
            }
            ExpoAudienceExtend audienceExtend = new ExpoAudienceExtend();
            audienceExtend.setAudienceId(dto.getId());
            audienceExtend.setExpoId(expoInfo.getId());
            audienceExtend.setCompanyId(expoInfo.getCompanyId());
            audienceExtend.setColName(r.getKey());
            audienceExtend.setFormId(config.getFormId());
            FormFieldEnums fieldEnums = FormFieldEnums.getByType(fieldDTO.getType());
            switch (fieldEnums) {
                case CHECKBOX:
                case SELECT:
                case RADIO:
                    List<String> strings = new ArrayList<>();
                    try {
                        strings.addAll(JSONArray.parseArray(r.getValue(), String.class));
                    } catch (Exception e) {
                        strings.add(r.getValue());
                    }
                    audienceExtend.setColValue(JSONObject.toJSONString(strings));
                    break;
                default:
                    audienceExtend.setColValue(r.getValue());
                    break;
            }
            extendList.add(audienceExtend);
        });
        if (CollectionUtils.isNotEmpty(extendList)) {
            expoAudienceExtendManager.saveOrUpdateBatch(extendList);
        }
        AppThreadLocal.setTenantId(dto.getTenantId());
        AppThreadLocal.setSourceCode("hsj");
        AddCustomerAudienceReq audienceReq = CopyObjectUtils.copyAtoB(dto, AddCustomerAudienceReq.class);
        audienceReq.setCustomerName(dto.getName());
        audienceReq.setMemberId(dto.getMemberId());
        Result<CustomerAudienceResp> audienceResp = customerAudienceClient.add(audienceReq);
        if (audienceResp.getCode() != 0) {
            throw new BusinessException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION);
        }
        dto.setCustomerId(audienceResp.getData().getId());
        expoAudienceManager.saveOrUpdate(dto);
        List<ExpoFormField> fieldList = formFieldManager.list(new LambdaQueryWrapper<ExpoFormField>()
                .eq(BaseEntity::getIsDeleted, CommonStatus.YesOrNoEnum.NO.getValue())
                .eq(ExpoFormField::getFormId, config.getExpoId())
        );
        ExpoAudienceScanVO vo = CopyObjectUtils.copyAtoB(dto, ExpoAudienceScanVO.class);
        vo.setFieldList(fieldList.stream().map(ExpoFormField::getColName).collect(Collectors.toList()));
        String qrcodeUrl = String.format(gateway + scanAudienceSignCode, dto.getId(), dto.getCompanyId(), dto.getExpoId());
        vo.setQrcodeUrl(qrcodeUrl);
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
        vo.setQrcodeImage(png);
        vo.setExtMap(extMap);
        vo.setExpoName(expoInfo.getExpoName());
        vo.setHallName(expoInfo.getHallName());
        vo.setStartTime(expoInfo.getStartTime());
        vo.setEndTime(expoInfo.getEndTime());
        // todo 观众注册不发邮件
//        Result<EmailTemplateResp> emailTemplate = ieMailFeign.getEmailTemplate(EmailAutoSendEventEnum.AUDIENCE_REGISTER.getCode(),
//                dto.getCompanyId());
//        if (emailTemplate.getCode() != 0) {
//            throw new BusinessException(emailTemplate.getCode(), emailTemplate.getMessage());
//        }
//        EmailTemplateResp resp = emailTemplate.getData();
//        if (Objects.nonNull(resp)) {
//            HashMap<String, String> paramMap = new HashMap<>();
//            paramMap.put("userName", dto.getName());
//            paramMap.put("companyName", dto.getCompanyName());
//            paramMap.put("QRcode", png);
//            EmailTemplateResp templateData = emailTemplate.getData();
//            SendEmailReq sendEmailReq = new SendEmailReq();
//            sendEmailReq.setTemplateId(templateData.getId());
//            sendEmailReq.setSubject(templateData.getSubject());
//            sendEmailReq.setContent(templateData.getContent());
//            sendEmailReq.setCompanyId(dto.getCompanyId());
//            sendEmailReq.setReceiver(dto.getEmail());
//            sendEmailReq.setServiceName(ServiceNameEnum.ECH_EXPO.getName());
//            sendEmailReq.setTemplateParams(paramMap);
//            Result<String> result = ieMailFeign.sendEmail(sendEmailReq);
//            if (result.getCode() != 0) {
//                throw new BusinessException(result.getCode(), result.getMessage());
//            }
//            ExpoAudienceEmailRecords emailRecords = new ExpoAudienceEmailRecords();
//            emailRecords.setEmail(dto.getEmail());
//            emailRecords.setAudienceId(dto.getId());
//            emailRecords.setExpoId(dto.getExpoId());
//            emailRecords.setCompanyId(dto.getCompanyId());
//            emailRecords.setContent(sendEmailReq.getContent());
//            emailRecords.setIsSend(false);
//            emailRecords.setBusinessCode(result.getData());
//            expoAudienceEmailRecordsManager.saveOrUpdate(emailRecords);
//        }
        return Result.build(vo);
    }


    @Override
    public ExpoAudienceScanVO registerSubmit(ExpoAudienceDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getMessage());
        }
        ExpoChannel expoChannel = expoChannelManager.getId(dto.getChannelId());
        if (Objects.isNull(expoChannel)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_NOT_EXIST.getMessage());
        }
        // 观众注册表单
        ExpoForm expoForm = getAudienceRegisterForm(dto.getExpoId());
        if(!expoForm.getFormCode().equals(dto.getFormCode())){
            // 提交表单与展会表单不一致
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }

        ExpoAudience audience = expoAudienceManager.getOne(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getEmail, dto.getEmail())
                .eq(ExpoAudience::getExpoId, dto.getExpoId())
        );
        dto.setChannelId(expoChannel.getId());
        if (Objects.nonNull(audience)) {
            dto.setId(audience.getId());
        }
        expoAudienceManager.saveOrUpdate(dto);
        // 保存表单
        ExpoReferenceForm expoReferenceForm = expoReferenceFormManager.getByGroupTypeBusinessId(dto.getId(),
                ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null != expoReferenceForm){
            // 移除之前表单
            expoReferenceFormManager.removeById(expoReferenceForm.getId());
            expoReferenceFormExtendManager.removeByRefFormId(expoReferenceForm.getId());
        }
        expoReferenceForm = CopyObjectUtils.copyAtoB(expoForm, ExpoReferenceForm.class);
        expoReferenceForm.setFormId(expoForm.getId());
        expoReferenceForm.setBusinessId(dto.getId());
        LocalDateTime now = LocalDateTime.now();
        expoReferenceForm.setSubmitTime(now);
        expoReferenceForm.setAuditTime(now);
        expoReferenceForm.setAuditStatus(ExpoReferenceFormEnum.AuditStatus.AUDIT_PASS.getCode());
        expoReferenceForm.setFormVersion(dto.getVersionNumber());
        expoReferenceFormManager.save(expoReferenceForm);
        // 保存表单明细
        List<ExpoReferenceFormExtend> referenceFormExtendList = new ArrayList<>();
        for(Map.Entry<String, String> entry : dto.getExtMap().entrySet()){
            String colValue = entry.getValue();
            String colName = entry.getKey();
            ExpoReferenceFormExtend rfx = new ExpoReferenceFormExtend();
            rfx.setColName(colName);
            rfx.setColValue(colValue);
            rfx.setReferenceFormId(expoReferenceForm.getId());
            referenceFormExtendList.add(rfx);
        }
        expoReferenceFormExtendManager.saveBatch(referenceFormExtendList);


        // 保存观众信息
        AddCustomerAudienceReq audienceReq = CopyObjectUtils.copyAtoB(dto, AddCustomerAudienceReq.class);
        audienceReq.setCustomerName(dto.getName());
        audienceReq.setMemberId(dto.getMemberId());
        Result<CustomerAudienceResp> audienceResp = customerAudienceClient.add(audienceReq);
        if (audienceResp.getCode() != 0) {
            throw new BusinessException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION);
        }
        CustomerAudienceResp customerAudienceResp = audienceResp.getData();
        dto.setCustomerId(customerAudienceResp.getId());
        dto.setCustomerCompanyId(customerAudienceResp.getCustomerCompanyId());
        expoAudienceManager.saveOrUpdate(dto);
        ExpoAudienceScanVO vo = CopyObjectUtils.copyAtoB(dto, ExpoAudienceScanVO.class);
        String scanAudienceSignCode = GatewayRoutingConstants.scanAudienceSignCode();
        String qrcodeUrl = String.format(gateway + scanAudienceSignCode, dto.getId(), dto.getCompanyId(), dto.getExpoId());
        vo.setQrcodeUrl(qrcodeUrl);
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
        vo.setQrcodeImage(png);
        vo.setExtMap(dto.getExtMap());
        vo.setExpoName(expoInfo.getExpoName());
        vo.setHallName(expoInfo.getHallName());
        vo.setStartTime(expoInfo.getStartTime());
        vo.setEndTime(expoInfo.getEndTime());
        return vo;
    }

    /**
     * 获取展会观众注册表单
     * @param expoId
     * @return
     */
    public ExpoForm getAudienceRegisterForm(Integer expoId){
        // 获取这个展会观众注册表单
        ExpoForm expoForm = expoFormManager.getForm(expoId, ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null == expoForm){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }else if(expoForm.getFormType() != ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode() ||
                expoForm.getIsEnable() == CommonStatus.StatusEnum.DISABLE.getValue()){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getMessage());
        }
        return expoForm;
    }

    @Override
    public Result edit(ExpoAudienceDTO dto) {
        ExpoAudience audience = expoAudienceManager.getOne(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudience::getId, dto.getId())
        );
        if (Objects.isNull(audience)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getMessage());
        }
        audience.setCompanyName(dto.getCompanyName());
        audience.setName(dto.getName());
        audience.setIsBuyer(dto.getIsBuyer());
        audience.setCompanyWebsite(dto.getCompanyWebsite());
        expoAudienceManager.saveOrUpdate(audience);

        ExpoReferenceForm audienceReferenceForm = expoReferenceFormManager.getByGroupTypeBusinessId(audience.getId(),
                ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null == audienceReferenceForm){
            ExpoForm expoForm = expoFormManager.getFormByFormType(audience.getExpoId(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode(), null);
            audienceReferenceForm.setFormId(expoForm.getId());
            audienceReferenceForm.setFormGroup(ExpoFormEnums.FormGroup.AUDIENCE.getCode());
            audienceReferenceForm.setFormType(ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
            audienceReferenceForm.setExpoId(audience.getExpoId());
            audienceReferenceForm.setBusinessId(audience.getId());
            audienceReferenceForm.setFormCode(expoForm.getFormCode());
            audienceReferenceForm.setFormVersion(dto.getVersionNumber());
            audienceReferenceForm.setSubmitTime(LocalDateTime.now());
        }
        expoReferenceFormManager.saveOrUpdate(audienceReferenceForm);
        expoReferenceFormExtendManager.removeByRefFormId(audienceReferenceForm.getId());

        List<ExpoReferenceFormExtend> extendList = new ArrayList<>();
        dto.getExtMap().entrySet().forEach(r -> {
            ExpoReferenceFormExtend extend = new ExpoReferenceFormExtend();
            extend.setReferenceFormId(audienceReferenceForm.getId());
            extend.setColName(r.getKey());
            extend.setColValue(r.getValue());
            extendList.add(extend);
        });
        expoReferenceFormExtendManager.saveOrUpdateBatch(extendList);
        return Result.build();
    }

    @Override
    public Result del(ExpoAudienceDTO dto) {
        expoAudienceManager.remove(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudience::getId, dto.getId())
        );
        expoAudienceExtendManager.remove(new LambdaQueryWrapper<ExpoAudienceExtend>()
                .eq(ExpoAudienceExtend::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudienceExtend::getAudienceId, dto.getId())
        );
        return Result.build();
    }

    @Override
    public Result<ExpoAudienceVO> sign(ExpoAudienceDTO dto) {
        ExpoInfo info = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getExpoStatus, ExpoStatusEnums.STARTED.getCode())
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
                .eq(ExpoInfo::getId, dto.getExpoId())
        );
        if (Objects.isNull(info)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_STATUS_UNSTART_EXPIRE.getMessage());
        }
        ExpoAudience audience = expoAudienceManager.getOne(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudience::getExpoId, info.getId())
                .eq(ExpoAudience::getId, dto.getId())
        );
        if (Objects.isNull(audience)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getMessage());
        }
        List<ExpoAudienceSign> signs = expoAudienceSignManager.list(new LambdaQueryWrapper<ExpoAudienceSign>()
                .eq(ExpoAudienceSign::getAudienceId, audience.getId())
                .eq(ExpoAudienceSign::getExpoId, info.getId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        Integer signCount = CollectionUtils.isNotEmpty(signs) ? signs.size() + 1 : 1;
        ExpoAudienceSign sign = new ExpoAudienceSign();
        sign.setAudienceId(audience.getId());
        sign.setExpoId(info.getId());
        sign.setCompanyId(info.getCompanyId());
        expoAudienceSignManager.saveOrUpdate(sign);
        List<ExpoFormField> fieldList = formFieldManager.list(new LambdaQueryWrapper<ExpoFormField>()
                .eq(ExpoFormField::getExpoId, info.getId())
        );
        List<String> fields = fieldList.stream().map(ExpoFormField::getColName).collect(Collectors.toList());
        List<ExpoAudienceExtend> extendList = expoAudienceExtendManager.list(new LambdaQueryWrapper<ExpoAudienceExtend>()
                .eq(ExpoAudienceExtend::getAudienceId, audience.getId())
        );
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
        Map<String, String> extMap = new HashMap<>();
        extendList.stream().forEach(r -> {
            FormFieldDTO fieldDTO = fieldDTOMap.get(r.getColName());
            if (Objects.isNull(fieldDTO)) {
                return;
            }
            extMap.put(r.getColName(), r.getColValue());
//            FormFieldEnums fieldEnums = FormFieldEnums.getByType(fieldDTO.getType());
//            switch (fieldEnums) {
//                case TEXT:
//                    extMap.put(r.getColName(), r.getColValue());
//                    break;
//                case CHECKBOX:
//                case SELECT:
//                case RADIO:
//                    extMap.put(r.getColName(), r.getColValue());
//                    break;
//            }
        });
        ExpoAudienceVO vo = CopyObjectUtils.copyAtoB(audience, ExpoAudienceVO.class);
        vo.setExtMap(extMap);
        vo.setFieldList(fields);
        vo.setSignCount(signCount);
        vo.setLastSignTime(CollectionUtils.isNotEmpty(signs) ? signs.stream().findFirst().get().getCreateTime() : null);
        return Result.build(vo);
    }

    @Override
    public void sendEmail(ExpoAudienceEmailRecordsDTO dto) {
        List<ExpoAudience> audiences = expoAudienceManager.list(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getCompanyId, dto.getCompanyId())
                .eq(ExpoAudience::getExpoId, dto.getExpoId())
                .in(ExpoAudience::getId, dto.getIds())
        );
        audiences.stream().forEach(r -> {
            String scanAudienceSignCode = GatewayRoutingConstants.scanAudienceSignCode();
            String qrcodeUrl = String.format(gateway + scanAudienceSignCode, r.getId(), r.getCompanyId(), r.getExpoId());
            QrConfig qrConfig = new QrConfig(200, 200);
            String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
            HashMap<String, String> paramMap = new HashMap<>();
            paramMap.put("userName", r.getName());
            paramMap.put("companyName", r.getCompanyName());
            paramMap.put("QRcode", png);
            SendEmailReq sendEmailReq = new SendEmailReq();
            sendEmailReq.setTemplateId(dto.getTemplateId());
            sendEmailReq.setSubject(dto.getSubject());
            sendEmailReq.setContent(dto.getContent());
            sendEmailReq.setConfigId(dto.getConfigId());
            sendEmailReq.setCompanyId(r.getCompanyId());
            sendEmailReq.setReceiver(r.getEmail());
            sendEmailReq.setServiceName(ServiceNameEnum.ECH_EXPO.getName());
            sendEmailReq.setTemplateParams(paramMap);
            log.info("发送邮件req:{}", JSONObject.toJSONString(sendEmailReq));
            Result<String> result = ieMailFeign.sendEmail(sendEmailReq);
            if (result.getCode() != 0) {
                throw new BusinessException(result.getCode(), result.getMessage());
            }
            ExpoAudienceEmailRecords emailRecords = new ExpoAudienceEmailRecords();
            emailRecords.setEmail(r.getEmail());
            emailRecords.setBusinessCode(result.getData());
            emailRecords.setAudienceId(r.getId());
            emailRecords.setExpoId(r.getExpoId());
            emailRecords.setCompanyId(r.getCompanyId());
            emailRecords.setContent(dto.getContent());
            emailRecords.setIsSend(false);
            expoAudienceEmailRecordsManager.saveOrUpdate(emailRecords);
        });
    }

    @Override
    public void downLoadTemplateExcel(HttpServletResponse response, RequestUser user) {
        StringBuilder sb = new StringBuilder().append(TemplateDownLoadEnums.TEMPLATE_IMPORT_AUDIENCE.getMessage())
                .append("-")
                .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        try {
            Locale locale = LocaleContextHolder.getLocale();
            ResourceBundle bundle = ResourceBundle.getBundle("messages", locale);
            String Multiple = bundle.getString("EXPO.AUDIENCE.IMPORT.MULTIPLE.REMARK");
            String SINGLE = bundle.getString("EXPO.AUDIENCE.IMPORT.SINGLE.REMARK");
            String createTime = bundle.getString("EXPO.AUDIENCE.IMPORT.TIME.REMARK");
            ExcelXSSFExportUtil.setResponseHeader(response, sb.toString());
            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, AudienceTemplateFiled.class)
                    .registerWriteHandler(new DynamicCommentWriteHandler(Lists.newArrayList(6, 8, 11, 12, 13, 15, 16, 17, 19, 21), SINGLE))
                    .registerWriteHandler(new DynamicCommentWriteHandler(Lists.newArrayList(9, 10, 14, 18, 22), Multiple))
                    .registerWriteHandler(new DynamicCommentWriteHandler(Lists.newArrayList(24), createTime))
                    .registerWriteHandler(new DropDownWriteHandler(7, 1, ExpoAudienceConstants.yesOrNo))
                    .sheet(TemplateDownLoadEnums.TEMPLATE_IMPORT_AUDIENCE.getMessage())
                    .doWrite(Lists.newArrayList());
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.error("导出失败：{}", e);
        }
    }

    /**
     * 导出数据
     * @param expoId 展会ID
     * @param response 输出流
     * @param user 用户信息
     * @param valueList 数据列表 valueList.add(Arrays.asList("姓名","年龄","表单列1","表单列2"));
     */
    @Override
    public void exportData(Integer expoId, HttpServletResponse response, RequestUser user,  List<List<String>> valueList) {
        ExpoForm form = expoFormManager.getForm(expoId, ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null == form) {
            throw new BusinessException(-1, "表单不存在");
        }
        // 获取表单
        List<ComponentResp> formComponentList = feignCommonManager.getFormComponents(form.getCompanyId(), form.getFormCode());

        StringBuilder sb = new StringBuilder().append(TemplateDownLoadEnums.TEMPLATE_IMPORT_AUDIENCE.getMessage())
                .append("-")
                .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        ExcelXSSFExportUtil.setResponseHeader(response, sb.toString());
        try {
            Locale locale = LocaleContextHolder.getLocale();
            ResourceBundle bundle = ResourceBundle.getBundle("messages", locale);
            String channel = bundle.getString("EXPO.AUDIENCE.IMPORT.CHANNEL");
            String createTime = bundle.getString("EXPO.AUDIENCE.IMPORT.TIME.REMARK");

            // 自定义表头
            List<List<String>> dynamicHead = new ArrayList<>();
            for(ComponentResp componentResp : formComponentList){
                StringBuilder label = new StringBuilder();
                if(CommonStatus.YesOrNoEnum.YES.getValue() == componentResp.getIsSystem()){
                    label.append("*");
                }
                label.append(componentResp.getAttr().get("label"));
                // 对应的系统字段名称
                String fieldName = componentResp.getColName();
                dynamicHead.add(Arrays.asList(fieldName, label.toString()));
            }

            // 固定字段
            dynamicHead.add(Arrays.asList("channel","*" + channel));
            dynamicHead.add(Arrays.asList("ip","ip"));
            dynamicHead.add(Arrays.asList("createTime","*" + createTime));


            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream)
                    .head(dynamicHead)
                    .inMemory(true)
                    .registerWriteHandler(new RowWriteHandler() {
                        @Override
                        public void afterRowCreate(RowWriteHandlerContext context) {
                            Row row = context.getRow();
                            // 检查是否为第一行（索引为0）
                            if (row.getRowNum() == 0) {
                                // 隐藏第一行
                                row.setZeroHeight(true);
                            }
                        }
                    })
                    .registerWriteHandler(new I18nCellWriteHandler())
                    .sheet(ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc())
                    .doWrite(valueList);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.error("导出失败：{}", e);
        }
    }


    @Override
    public AudienceImportResultVO checkImportExcel(ExpoAudienceImportParam param, RequestUser requestUser) {
        AudienceImportResultVO vo = new AudienceImportResultVO();
        try {
            Map<String, String> yesOrNoMap = new HashMap<>();
            yesOrNoMap.put("NO", "0");
            yesOrNoMap.put("YES", "1");
            DateTimeExceptionListener awareListener = new DateTimeExceptionListener();
            EasyExcel.read(param.getFile().getInputStream(),
                            AudienceTemplateFiled.class, awareListener)
                    .sheet(1)
                    .doRead();
            Map<Integer, String> errorMap = awareListener.getErrorMap();
            if (CollectionUtils.isNotEmpty(errorMap)) {
                ArrayList<ImportCheckMsgVO> objects = Lists.newArrayList();
                errorMap.entrySet().stream().forEach(r -> {
                    ImportCheckMsgVO checkMsgVO = new ImportCheckMsgVO();
                    checkMsgVO.setMsgList(Lists.newArrayList(r.getValue()));
                    checkMsgVO.setIsOk(Boolean.FALSE);
                    checkMsgVO.setRowNo(r.getKey());
                    objects.add(checkMsgVO);
                });
                vo.setErrorNum(errorMap.size());
                vo.setSuccessNum(awareListener.getSuccessList().size());
                vo.setMsgList(objects);
                return vo;
            }
            List<AudienceTemplateFiled> audienceTemplateFileds = awareListener.getSuccessList();
            if (CollectionUtils.isEmpty(audienceTemplateFileds)) {
                List<String> msgs = new ArrayList<>();
                msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getMessage());
                ImportCheckMsgVO checkMsgVO = new ImportCheckMsgVO();
                checkMsgVO.setMsgList(msgs);
                checkMsgVO.setIsOk(Boolean.FALSE);
                vo.setSuccessNum(0);
                vo.setMsgList(Lists.newArrayList(checkMsgVO));
                return vo;
            }
            Map<String, List<AudienceTemplateFiled>> listMap = audienceTemplateFileds.stream().filter(r -> StringUtils.isNotBlank(r.getEmail()))
                    .collect(Collectors.groupingBy(AudienceTemplateFiled::getEmail));
            List<String> channelNameList = audienceTemplateFileds.stream().filter(r -> StringUtils.isNotBlank(r.getChannelName()))
                    .map(AudienceTemplateFiled::getChannelName).collect(Collectors.toList());
            List<ExpoChannel> channels = expoChannelManager.list(new LambdaQueryWrapper<ExpoChannel>()
                    .in(CollectionUtils.isNotEmpty(channelNameList), ExpoChannel::getChannelName, channelNameList)
                    .eq(ExpoChannel::getCompanyId, requestUser.getCompanyId())
            );
            Map<String, ExpoChannel> channelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(channels)) {
                List<ExpoChannelConfig> channelConfigs = expoChannelConfigManager.list(new LambdaQueryWrapper<ExpoChannelConfig>()
                        .in(ExpoChannelConfig::getChannelId, channels.stream().map(ExpoChannel::getId).collect(Collectors.toSet()))
                        .eq(ExpoChannelConfig::getExpoId, param.getExpoId())
                );
                Set<Integer> channelIds = channelConfigs.stream().map(ExpoChannelConfig::getChannelId).collect(Collectors.toSet());
                channelMap.putAll(channels.stream().filter(r -> channelIds.contains(r.getId())).collect(Collectors.toMap(ExpoChannel::getChannelName, r -> r)));
            }
            List<String> emailList = audienceTemplateFileds.stream().filter(r -> StringUtils.isNotBlank(r.getEmail()))
                    .map(AudienceTemplateFiled::getEmail).collect(Collectors.toList());
            List<ExpoAudience> expoAudiences = expoAudienceManager.list(new LambdaQueryWrapper<ExpoAudience>()
                    .in(CollectionUtils.isNotEmpty(emailList), ExpoAudience::getEmail, emailList)
                    .eq(ExpoAudience::getExpoId, param.getExpoId())
                    .eq(ExpoAudience::getCompanyId, requestUser.getCompanyId())
            );
            Map<String, ExpoAudience> audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getEmail, r -> r));
            // 错误的导入数量
            int errorNum = 0;
            int successNum = 0;
            Integer rowIndex = 1;
            List<ImportCheckMsgVO> msgList = new ArrayList<>();
            List<ExpoAudience> audiences = new ArrayList<>();
            Map<String, Map<String, String>> emailMap = new HashMap<>();
            String email_rex = "^[a-zA-Z0-9äöüéñ_!#$%&'*+/=?`{|}~^.-]+@[a-zA-Z0-9äöüéñ_!#$%&'*+/=?`{|}~^.-]+\\.[a-zA-Zäöüéñ]{2,30}$";
//            String phone_rex = "^((?:\\+|00)(\\d{1,4})[\\s-]*(\\d{6,15}))|((?:\\+86|86|0086)?[\\s-]*1[0-9]\\d{20})$";
            for (AudienceTemplateFiled r : audienceTemplateFileds) {
                List<String> msgs = new ArrayList<>();
                if (StringUtils.isBlank(r.getName())) {
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NAME_IS_NOT_NULL.getMessage());
                }
                if (StringUtils.isBlank(r.getEmail())) {
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_IS_NOT_NULL.getMessage());
                } else {
                    boolean matches = Pattern.matches(email_rex, r.getEmail());
                    if (!matches) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_FORMAT_ERROR.getMessage());
                    }
                    if (r.getEmail().length() > 128) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_LENGTH_TOO_LONG.getMessage());
                    }
                    ExpoAudience expoAudience = audienceMap.get(r.getEmail());
                    if (Objects.nonNull(expoAudience)) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_EXIST.getMessage());
                    }
                }
                ExpoChannel channel = null;
                if (StringUtils.isNotBlank(r.getChannelName())) {
                    channel = channelMap.get(r.getChannelName());
                    if (Objects.isNull(channel)) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getMessage());
                    }
                }
                List<AudienceTemplateFiled> fileds = listMap.get(r.getEmail());
                if (CollectionUtils.isNotEmpty(fileds) && fileds.size() > 1) {
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_REPEAT_ERROR.getMessage());
                }
                if (StringUtils.isBlank(r.getPhone())) {
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_PHONE_IS_NOT_NULL.getMessage());
                }
                if (StringUtils.isNotBlank(r.getAsBuyer())) {
                    String s = yesOrNoMap.get(r.getAsBuyer().toUpperCase());
                    if (StringUtils.isBlank(s)) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_ASBUYER_VALUES.getMessage());
                    }
                    r.setAsBuyer(s);
                }
//                else {
//                    boolean matches = Pattern.matches(phone_rex, r.getPhone());
//                    if (!matches) {
//                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_PHONE_FORMAT_ERROR.getMessage());
//                    }
//                }
                ImportCheckMsgVO checkMsgVO = new ImportCheckMsgVO();
                checkMsgVO.setRowNo(rowIndex);
                checkMsgVO.setName(r.getName());
                checkMsgVO.setEmail(r.getEmail());
                checkMsgVO.setPhone(r.getPhone());
                if (CollectionUtils.isNotEmpty(msgs)) {
                    checkMsgVO.setIsOk(Boolean.FALSE);
                    errorNum++;
                } else {
                    msgs = Lists.newArrayList("pass");
                    successNum++;
                    checkMsgVO.setIsOk(Boolean.TRUE);
                    ExpoAudience audience = CopyObjectUtils.copyAtoB(r, ExpoAudience.class);
                    audience.setCompanyId(requestUser.getCompanyId());
                    audience.setExpoId(param.getExpoId());
                    if (Objects.nonNull(channel)) {
                        audience.setChannelId(channel.getId());
                    }
                    audience.setIsCrm(false);
                    audience.setMemberId(requestUser.getMemberId());
                    AudienceExtendFiled extendFiled = CopyObjectUtils.copyAtoB(r, AudienceExtendFiled.class);
                    List<String> strings = FormFieldsUtil.fieldNoList(AudienceExtendFiled.class);
                    Map<String, String> extendMap = new HashMap<>();
                    strings.forEach(s -> {
                        String fieldValue = FormFieldsUtil.getFieldValue(extendFiled, s);
                        if (StringUtils.isNotBlank(fieldValue)) {
                            extendMap.put(s, fieldValue);
                        }
                    });
                    audience.setExtMap(extendMap);
                    if (Objects.nonNull(r.getCreateTime()) && StringUtils.isNotBlank(param.getZoneId())) {
                        LocalDateTime createTime = r.getCreateTime();
                        // 创建带时区的日期时间
                        ZonedDateTime zonedDateTime = createTime.atZone(ZoneId.of(param.getZoneId()));
                        // 转换为UTC时间
                        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
                        audience.setCreateTime(utcDateTime.toLocalDateTime());
                    } else {
                        audience.setCreateTime(LocalDateTime.now());
                    }
                    audience.setUpdateTime(LocalDateTime.now());
                    audiences.add(audience);
                }
                checkMsgVO.setMsgList(msgs);
                msgList.add(checkMsgVO);
                rowIndex++;
            }
            vo.setErrorNum(errorNum);
            vo.setSuccessNum(successNum);
            vo.setTotal(audienceTemplateFileds.size());
            vo.setMsgList(msgList);
            vo.setAudienceList(audiences);
            return vo;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导入观众异常:{}", e.getStackTrace());
        }
        return null;
    }

    public void test(ExpoAudienceImportParam param){
        try {
            InputStream inputStream = param.getFile().getInputStream();
            AudienceExcelImportListener listener = new AudienceExcelImportListener();
            // 使用 EasyExcel 动态读取数据
            EasyExcel.read(inputStream)
                    .sheet(0) // 读取第一个 sheet
                    .headRowNumber(0) // 表头在第0行
                    .registerReadListener(listener)
                    .doRead();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public AudienceImportResultVO checkImportExcelV2(ExpoAudienceImportParam param, RequestUser requestUser) {
        ExpoForm form = expoFormManager.getForm(param.getExpoId(), ExpoFormEnums.FormGroup.AUDIENCE.getCode(), ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode());
        if(null == form) {
            throw new BusinessException(-1, "表单不存在");
        }
        // 查询表单
        FormResp formResp = feignCommonManager.getForm(requestUser.getCompanyId(), form.getFormCode());
        try {
            Map<String, Integer> yesOrNoMap = new HashMap<>();
            yesOrNoMap.put("NO", 0);
            yesOrNoMap.put("YES", 1);
            InputStream inputStream = param.getFile().getInputStream();
            AudienceExcelImportListener listener = new AudienceExcelImportListener();
            // 使用 EasyExcel 动态读取数据
            EasyExcel.read(inputStream)
                    .sheet(0) // 读取第一个 sheet
                    .headRowNumber(0) // 表头在第0行
                    .registerReadListener(listener)
                    .doRead();
            AudienceImportResultVO vo = new AudienceImportResultVO();
            List<AudienceExcelImportDTO> dataList = listener.getDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                List<String> msgs = new ArrayList<>();
                msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getMessage());
                ImportCheckMsgVO checkMsgVO = new ImportCheckMsgVO();
                checkMsgVO.setMsgList(msgs);
                checkMsgVO.setIsOk(Boolean.FALSE);
                vo.setSuccessNum(0);
                vo.setMsgList(Lists.newArrayList(checkMsgVO));
                return vo;
            }
            Map<String, List<AudienceExcelImportDTO>> listMap = dataList.stream().filter(r -> StringUtils.isNotBlank(r.getEmail()))
                    .collect(Collectors.groupingBy(AudienceExcelImportDTO::getEmail));
            List<String> channelNameList = dataList.stream().filter(r -> StringUtils.isNotBlank(r.getChannelName()))
                    .map(AudienceExcelImportDTO::getChannelName).collect(Collectors.toList());
            List<ExpoChannel> channels = expoChannelManager.getList(requestUser.getCompanyId(), channelNameList);
            Map<String, ExpoChannel> channelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(channels)) {
                List<ExpoChannelConfig> channelConfigs = expoChannelConfigManager.list(new LambdaQueryWrapper<ExpoChannelConfig>()
                        .in(ExpoChannelConfig::getChannelId, channels.stream().map(ExpoChannel::getId).collect(Collectors.toSet()))
                        .eq(ExpoChannelConfig::getExpoId, param.getExpoId())
                );
                Set<Integer> channelIds = channelConfigs.stream().map(ExpoChannelConfig::getChannelId).collect(Collectors.toSet());
                channelMap.putAll(channels.stream().filter(r -> channelIds.contains(r.getId())).collect(Collectors.toMap(ExpoChannel::getChannelName, r -> r)));
            }
            List<String> emailList = dataList.stream().filter(r -> StringUtils.isNotBlank(r.getEmail()))
                    .map(AudienceExcelImportDTO::getEmail).collect(Collectors.toList());
            Map<String, ExpoAudience> audienceMap = null;
            if (CollectionUtils.isNotEmpty(emailList)) {
                List<ExpoAudience> expoAudiences = expoAudienceManager.listByEmails(emailList, param.getExpoId(), requestUser.getCompanyId());
                audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getEmail, r -> r));
            }
            // 错误的导入数量
            int errorNum = 0;
            int successNum = 0;
            Integer rowIndex = 1;
            List<ImportCheckMsgVO> msgList = new ArrayList<>();
            List<ExpoAudience> audiences = new ArrayList<>();
            Map<String, Map<String, String>> emailMap = new HashMap<>();
            String email_rex = "^[a-zA-Z0-9äöüéñ_!#$%&'*+/=?`{|}~^.-]+@[a-zA-Z0-9äöüéñ_!#$%&'*+/=?`{|}~^.-]+\\.[a-zA-Zäöüéñ]{2,30}$";

            for (AudienceExcelImportDTO r : dataList) {
                List<String> msgs = new ArrayList<>();
                if (StringUtils.isBlank(r.getName())) {
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NAME_IS_NOT_NULL.getMessage());
                }
                if(StringUtils.isBlank(r.getEmail()) || StringUtils.isBlank(r.getPhone())){
                    msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_PHONE_EMAIL_IS_NOT_NULL.getMessage());
                }else{
                    if (StringUtils.isBlank(r.getEmail())) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_IS_NOT_NULL.getMessage());
                    } else {
                        boolean matches = Pattern.matches(email_rex, r.getEmail());
                        if (!matches) {
                            msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_FORMAT_ERROR.getMessage());
                        }
                        if (r.getEmail().length() > 128) {
                            msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_LENGTH_TOO_LONG.getMessage());
                        }
                        ExpoAudience expoAudience = audienceMap.get(r.getEmail());
                        if (Objects.nonNull(expoAudience)) {
                            msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_EXIST.getMessage());
                        }
                        List<AudienceExcelImportDTO> fileds = listMap.get(r.getEmail());
                        if (CollectionUtils.isNotEmpty(fileds) && fileds.size() > 1) {
                            msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_EMAIL_REPEAT_ERROR.getMessage());
                        }
                    }
                    if (StringUtils.isBlank(r.getPhone())) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_PHONE_IS_NOT_NULL.getMessage());
                    }
                }
                ExpoChannel channel = null;
                if (StringUtils.isNotBlank(r.getChannelName())) {
                    channel = channelMap.get(r.getChannelName());
                    if (Objects.isNull(channel)) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getMessage());
                    }
                }
                if (StringUtils.isNotBlank(r.getAsBuyer())) {
                    Integer isBuyer = yesOrNoMap.get(r.getAsBuyer().toUpperCase());
                    if (null == isBuyer) {
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_ASBUYER_VALUES.getMessage());
                    }else{
                        r.setIsBuyer(isBuyer);
                    }
                }
                if(StringUtils.isNotBlank(r.getCreateTimeText())){
                    // 1. 定义日期时间格式（与字符串格式完全匹配）
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    try {
                        // 2. 解析字符串为 LocalDateTime
                        LocalDateTime createTime = LocalDateTime.parse(r.getCreateTimeText(), formatter);
                        r.setCreateTime(createTime);
                        // 输出：转换结果: 2025-01-01T11:33:44
                    } catch (DateTimeParseException e) {
                        // 处理格式不匹配异常（例如字符串格式错误）
                        System.err.println("日期格式错误，无法转换: " + e.getMessage());
                        msgs.add(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_TIME_IS_ERROR.getMessage());
                    }
                }
                ImportCheckMsgVO checkMsgVO = new ImportCheckMsgVO();
                checkMsgVO.setRowNo(rowIndex);
                checkMsgVO.setName(r.getName());
                checkMsgVO.setEmail(r.getEmail());
                checkMsgVO.setPhone(r.getPhone());
                if (CollectionUtils.isNotEmpty(msgs)) {
                    checkMsgVO.setIsOk(Boolean.FALSE);
                    errorNum++;
                } else {
                    msgs = Lists.newArrayList("pass");
                    successNum++;
                    checkMsgVO.setIsOk(Boolean.TRUE);
                    ExpoAudience audience = CopyObjectUtils.copyAtoB(r, ExpoAudience.class);
                    audience.setCompanyId(requestUser.getCompanyId());
                    audience.setExpoId(param.getExpoId());
                    audience.setFormCode(formResp.getFormCode());
                    audience.setFormId(form.getId());
                    if (Objects.nonNull(channel)) {
                        audience.setChannelId(channel.getId());
                    }
                    audience.setIsCrm(false);
                    audience.setMemberId(requestUser.getMemberId());
                    if (Objects.nonNull(r.getCreateTime()) && StringUtils.isNotBlank(param.getZoneId())) {
                        LocalDateTime createTime = r.getCreateTime();
                        // 创建带时区的日期时间
                        ZonedDateTime zonedDateTime = createTime.atZone(ZoneId.of(param.getZoneId()));
                        // 转换为UTC时间
                        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
                        audience.setCreateTime(utcDateTime.toLocalDateTime());
                    } else {
                        audience.setCreateTime(LocalDateTime.now());
                    }
                    audience.setUpdateTime(LocalDateTime.now());
                    audiences.add(audience);
                }
                checkMsgVO.setMsgList(msgs);
                msgList.add(checkMsgVO);
                rowIndex++;
            }
            vo.setErrorNum(errorNum);
            vo.setSuccessNum(successNum);
            vo.setTotal(dataList.size());
            vo.setMsgList(msgList);
            vo.setAudienceList(audiences);
            return vo;
        } catch (IOException e) {
            log.error("导入观众异常:{}", e.getStackTrace());
        }
        return null;
    }

    @Override
    public AudienceImportResultVO importExel(ExpoAudienceImportParam param, RequestUser requestUser) {
        AudienceImportResultVO vo = checkImportExcel(param, requestUser);
        if (CollectionUtils.isNotEmpty(vo.getMsgList())) {
            List<ImportCheckMsgVO> checkMsgVOS = vo.getMsgList().stream().filter(r -> r.getIsOk().equals(Boolean.FALSE))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(checkMsgVOS)) {
                return vo;
            }
            List<ExpoAudience> audienceList = vo.getAudienceList();
            syncSaveAudience(audienceList, requestUser);
        }
        return vo;
    }

    @Async("asyncServiceExecutor")
    public void syncSaveAudience(List<ExpoAudience> audienceList, RequestUser requestUser) {
        expoAudienceManager.saveOrUpdateBatch(audienceList);
        List<ExpoAudienceExtend> extendList = new ArrayList<>();
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
        audienceList.stream().forEach(r -> {
            Map<String, String> extMap = r.getExtMap();
            extMap.entrySet().stream().forEach(s -> {
                FormFieldDTO fieldDTO = fieldDTOMap.get(s.getKey());
                if (Objects.isNull(fieldDTO)) {
                    return;
                }
                ExpoAudienceExtend audienceExtend = new ExpoAudienceExtend();
                audienceExtend.setCompanyId(requestUser.getCompanyId());
                audienceExtend.setAudienceId(r.getId());
                audienceExtend.setExpoId(r.getExpoId());
                audienceExtend.setColName(s.getKey());
//                FormFieldEnums fieldEnums = FormFieldEnums.getByType(fieldDTO.getType());
//                switch (fieldEnums) {
//                    case CHECKBOX:
//                    case SELECT:
//                    case RADIO:
//                        List<String> strings = new ArrayList<>();
//                        try {
//                            strings.addAll(JSONArray.parseArray(s.getValue(), String.class));
//                        } catch (Exception e) {
//                            strings.add(s.getValue());
//                        }
//                        audienceExtend.setColValue(JSONObject.toJSONString(strings));
//                        break;
//                    default:
//                        audienceExtend.setColValue(s.getValue());
//                        break;
//                }
                extendList.add(audienceExtend);
            });
        });
        if (CollectionUtils.isNotEmpty(extendList)) {
            expoAudienceExtendManager.saveOrUpdateBatch(extendList);
        }
    }

    public AudienceImportResultVO importExelV2(ExpoAudienceImportParam param, RequestUser requestUser) {
        AudienceImportResultVO vo = checkImportExcelV2(param, requestUser);
        if (CollectionUtils.isNotEmpty(vo.getMsgList())) {

        }
        if (CollectionUtils.isNotEmpty(vo.getMsgList())) {
            List<ImportCheckMsgVO> checkMsgVOS = vo.getMsgList().stream().filter(r -> r.getIsOk().equals(Boolean.FALSE))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(checkMsgVOS)) {
                return vo;
            }
            List<ExpoAudience> audienceList = vo.getAudienceList();
            syncSaveAudience(audienceList, requestUser);
        }
        return vo;
    }

    @Async("asyncServiceExecutor")
    public void syncSaveAudienceV2(List<ExpoAudience> audienceList, RequestUser requestUser, ExpoForm expoForm) {
        expoAudienceManager.saveOrUpdateBatch(audienceList);
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
        LocalDateTime submitTime = LocalDateTime.now();
        audienceList.stream().forEach(r -> {
            if(CollectionUtils.isNotEmpty(r.getExtMap())){
                ExpoReferenceForm expoReferenceForm = new ExpoReferenceForm();
                expoReferenceForm.setFormId(expoForm.getId());
                expoReferenceForm.setExpoId(expoForm.getExpoId());
                expoReferenceForm.setFormGroup(expoForm.getFormGroup());
                expoReferenceForm.setFormType(expoForm.getFormType());
                expoReferenceForm.setSubmitTime(submitTime);
                expoReferenceForm.setBusinessId(r.getId());
                expoReferenceFormManager.save(expoReferenceForm);

                List<ExpoReferenceFormExtend> extendList = new ArrayList<>();
                r.getExtMap().entrySet().stream().forEach(s -> {
                    ExpoReferenceFormExtend expoReferenceFormExtend = new ExpoReferenceFormExtend();
                    expoReferenceFormExtend.setReferenceFormId(expoReferenceForm.getId());
                    expoReferenceFormExtend.setColName(s.getKey());
                    expoReferenceFormExtend.setColValue(s.getValue());
                    extendList.add(expoReferenceFormExtend);
                });
                expoReferenceFormExtendManager.saveBatch(extendList);
            }
        });
    }

    @Override
    public void exportExcel(HttpServletResponse response, ExpoAudienceListFilterExportParam param, RequestUser user) {
        try {
            ExpoInfo expoInfo = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                    .eq(ExpoInfo::getCompanyId, user.getCompanyId())
                    .eq(ExpoInfo::getId, param.getExpoId())
            );

            List<ExpoAudienceDTO> records = records(param, user.getCompanyId());
            Map<Integer, Map<String, ExpoAudienceExtend>> mapMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(records)) {
                Set<Integer> audienceIds = records.stream().map(ExpoAudience::getId).collect(Collectors.toSet());
                List<ExpoAudienceExtend> extendList = expoAudienceExtendManager.list(new LambdaQueryWrapper<ExpoAudienceExtend>()
                        .in(CollectionUtils.isNotEmpty(audienceIds), ExpoAudienceExtend::getAudienceId, audienceIds)
                        .eq(ExpoAudienceExtend::getExpoId, param.getExpoId())
                );
                mapMap.putAll(extendList.stream().collect(Collectors.
                        groupingBy(ExpoAudienceExtend::getAudienceId, Collectors.toMap(ExpoAudienceExtend::getColName, r -> r))));
            }
            List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
            Map<String, FormFieldDTO> fieldDTOMap = fieldDTOS.stream().collect(Collectors.toMap(FormFieldDTO::getColName, r -> r));
            List<AudienceExportHead> heads = CopyObjectUtils.copyAlistToBlist(records, AudienceExportHead.class);
            TimeZone timeZone = TimeZone.getTimeZone(param.getZoneId());
            heads.stream().forEach(r -> {
                LocalDateTime createTime = r.getCreateTime();
                LocalDateTime dateTime = createTime.toInstant(ZoneOffset.UTC).atZone(timeZone.toZoneId()).toLocalDateTime();
                r.setCreateTime(dateTime);
                if (Objects.nonNull(r.getLastSignTime())) {
                    r.setLastSignTime(r.getLastSignTime().toInstant(ZoneOffset.UTC).atZone(timeZone.toZoneId()).toLocalDateTime());
                }
                Map<String, String> extMap = new HashMap<>();
                Map<String, ExpoAudienceExtend> listMap = mapMap.get(r.getId());
                if (CollectionUtils.isNotEmpty(listMap)) {
                    listMap.entrySet().stream().forEach(s -> {
                        FormFieldDTO fieldDTO = fieldDTOMap.get(s.getKey());
                        if (Objects.isNull(fieldDTO)) {
                            return;
                        }
                        FormFieldEnums fieldEnums = FormFieldEnums.getByType(fieldDTO.getType());
                        switch (fieldEnums) {
                            case TEXT:
                                extMap.put(s.getKey(), s.getValue().getColValue());
                                break;
                            case CHECKBOX:
                            case SELECT:
                            case RADIO:
                                try {
                                    List<String> strings = JSONArray.parseArray(s.getValue().getColValue(), String.class);
                                    extMap.put(s.getKey(), strings.stream().collect(Collectors.joining(",")));
                                } catch (Exception e) {
                                    extMap.put(s.getKey(), s.getValue().getColValue());
                                }
                                break;
                        }
                    });
                }
                BeanUtil.copyProperties(extMap, r);
            });
            StringBuilder sb = new StringBuilder()
                    .append(expoInfo.getExpoName())
                    .append("_")
                    .append(TemplateDownLoadEnums.TEMPLATE_EXPORT_AUDIENCE.getMessage())
                    .append("_")
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            ExcelXSSFExportUtil.setResponseHeader(response, sb.toString());
            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, AudienceExportHead.class)
                    .sheet()
                    .doWrite(heads);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 观众信息同步至crm
     */
    @Override
    public void syncCrm() {
        List<ExpoAudience> audiences = expoAudienceManager.list(new LambdaQueryWrapper<ExpoAudience>()
                .eq(ExpoAudience::getIsCrm, CommonStatus.YesOrNoEnum.NO.getValue())
                .isNotNull(ExpoAudience::getMemberId)
                .last("limit 50")
        );
        if (CollectionUtils.isEmpty(audiences)) {
            return;
        }

        Set<String> has = new HashSet<>();
        audiences.stream().forEach(r -> {
            if (has.contains(r.getCompanyId() + "_" + r.getEmail())) {
                return;
            }
        });
        Map<String, List<ExpoAudience>> listMap = audiences.stream().collect(Collectors.groupingBy(ExpoAudience::getTenantId));
        listMap.entrySet().stream().forEach(t -> {
            AppThreadLocal.setSourceCode("hsj");
            AppThreadLocal.setTenantId(t.getKey());
            List<AddCustomerAudienceReq> audienceReqs = new ArrayList<>();
            t.getValue().stream().forEach(r -> {
                AddCustomerAudienceReq audienceReq = CopyObjectUtils.copyAtoB(r, AddCustomerAudienceReq.class);
                audienceReq.setCustomerName(r.getName());
                audienceReqs.add(audienceReq);
            });
            Result<List<CustomerAudienceResp>> listResult = customerAudienceClient.batchAdd(audienceReqs);
            if (listResult.getCode() != 0) {
                throw new BusinessException(listResult.getCode(), listResult.getMessage());
            }
            Map<String, CustomerAudienceResp> respMap = listResult.getData().stream().collect(Collectors
                    .toMap(r -> r.getCompanyId() + "_" + r.getEmail(), r -> r, (r1, r2) -> r1));
            audiences.stream().forEach(r -> {
                CustomerAudienceResp customerAudienceResp = respMap.get(r.getCompanyId() + "_" + r.getEmail());
                if (Objects.nonNull(customerAudienceResp)) {
                    r.setCustomerId(customerAudienceResp.getId());
                    r.setCustomerCompanyId(customerAudienceResp.getCustomerCompanyId());
                    r.setIsCrm(true);
                }
            });
            expoAudienceManager.saveOrUpdateBatch(audiences);
        });
    }

    /**
     * 查询观众列表
     *
     * @param param
     * @param companyId
     * @return
     */
    public List<ExpoAudienceDTO> records(ExpoAudienceListFilterExportParam param, Integer companyId) {
        ExpoAudiencePageDTO dto = new ExpoAudiencePageDTO();
        dto.setIdList(param.getIdList());
        dto.setExpoId(param.getExpoId());
        dto.setKeywords(param.getKeywords());
        dto.setCompanyId(companyId);
        Set<String> fieldList = expoAudienceExtendManager.fieldList(param.getExpoId(), null);
        dto.setFieldList(Lists.newArrayList(fieldList));
        List<CollectFilterParam> filters = param.getFilters();
        String asBuyer = ExpoAudienceQueryConstants.asBuyer;
        List<String> selectList = Lists.newArrayList(FormFieldEnums.SELECT.getType(),
                FormFieldEnums.RADIO.getType(),
                FormFieldEnums.CHECKBOX.getType()
        );
        if (CollectionUtils.isNotEmpty(filters)) {
            List<CollectConditionParam> conditions = filters.stream().map(CollectFilterParam::getConditions)
                    .flatMap(List::stream).collect(Collectors.toList());
            CollectConditionParam condition = conditions.stream().filter(r -> r.getColName().equals(asBuyer))
                    .findFirst().orElse(null);
            if (Objects.nonNull(condition) && condition.getValue().size() > 0) {
                dto.setBuyerList(condition.getValue().stream().map(r -> Objects.nonNull(r) ? r.toString() : null).collect(Collectors.toList()));
            }
            //自定义表单下拉框
            List<CollectConditionParam> filterConditions = conditions.stream().filter(r -> selectList.contains(r.getComponentType()) &&
                    !r.getColName().equals(asBuyer)).collect(Collectors.toList());
            filters.stream().forEach(r -> {
                List<CollectConditionParam> collect = r.getConditions().stream().filter(s -> !selectList.contains(s.getComponentType()))
                        .collect(Collectors.toList());
                r.setConditions(collect);
            });
            List<CollectFilter> collectFilters = CopyObjectUtils.copyAlistToBlist(filters, CollectFilter.class);
            StringBuilder stringBuilder = new StringBuilder();
            filterConditions.stream().forEach(r -> {
                stringBuilder.append(" and ");
                if (CollectionUtils.isNotEmpty(r.getValue()) && r.getValue().size() > 0) {
                    stringBuilder.append(" case when JSON_VALID(" + r.getColName() + ") = 0 then " + r.getColName() + " in (");
                    StringBuilder value1 = new StringBuilder("");
                    for (int i = 0; i < r.getValue().size(); i++) {
                        value1.append("'").append(r.getValue().get(i)).append("'");
                        if (i != r.getValue().size() - 1) {
                            value1.append(",");
                        }
                    }
                    stringBuilder.append(value1 + ")");
                    stringBuilder.append("when JSON_VALID(" + r.getColName() + ") = 1 then JSON_CONTAINS(" + r.getColName() +
                            ", JSON_ARRAY(" + value1 + "))"
                    );
                    stringBuilder.append("else false end ");
                }
            });
            String buildConditionNew = SqlUtils.buildConditionNew(collectFilters, "expo_audience_field", null, dto.getCompanyId());
            if (StringUtils.isNotBlank(buildConditionNew)) {
                stringBuilder.append(" and ");
            }
            stringBuilder.append(buildConditionNew);
            if (StringUtils.isNotBlank(stringBuilder.toString())) {
                dto.setWhereSqlStr(stringBuilder.toString());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getSort()) && param.getSort().size() > 0) {
            dto.setSortSqlStr(SqlUtils.buildSortNew(param.getSort(), "expo_audience_field", null, dto.getCompanyId()));
        }
        List<ExpoAudienceDTO> records = expoAudienceManager.listAudience(dto);
        return records;
    }

    @Override
    public Result<List<ExpoCodeValueVO>> dictByColName(String colName, Integer companyId, Long expoId) {
        List<ExpoAudienceExtend> extendList = listByColName(companyId, colName, expoId);
        List<ExpoCodeValueVO> vos = new ArrayList<>();
        Set<String> hasSet = new HashSet<>();
        extendList.stream().forEach(r -> {
            try {
                List<String> strings = JSONArray.parseArray(r.getColValue(), String.class);
                strings.stream().forEach(s -> {
                    if (!hasSet.contains(s)) {
                        ExpoCodeValueVO vo = new ExpoCodeValueVO();
                        vo.setName(s);
                        vo.setCode(s);
                        vos.add(vo);
                        hasSet.add(s);
                    }
                });
            } catch (Exception e) {
                if (!hasSet.contains(r.getColValue())) {
                    ExpoCodeValueVO vo = new ExpoCodeValueVO();
                    vo.setName(r.getColValue());
                    vo.setCode(r.getColValue());
                    vos.add(vo);
                    hasSet.add(r.getColValue());
                }
            }
        });
        return Result.build(vos);
    }

    @Override
    public Result<ExpoDictCodeValueVO> dictList() {
        ExpoDictCodeValueVO vo = new ExpoDictCodeValueVO();
        vo.setActivitiesList(convertToList(Arrays.asList(ExpoAudienceConstants.participate)));
        vo.setBudgetList(convertToList(Arrays.asList(ExpoAudienceConstants.budget)));
        vo.setCompanySizeList(convertToList(Arrays.asList(ExpoAudienceConstants.companySize)));
        vo.setExpoRoleList(convertToList(Arrays.asList(ExpoAudienceConstants.expoRole)));
        vo.setCooperationModelList(convertToList(Arrays.asList(ExpoAudienceConstants.cooperationModel)));
        vo.setIndustryList(convertToList(Arrays.asList(ExpoAudienceConstants.audienceIndustry)));
        vo.setInterestsList(convertToList(Arrays.asList(ExpoAudienceConstants.interests)));
        vo.setPlansList(convertToList(Arrays.asList(ExpoAudienceConstants.plans)));
        vo.setPositionsList(convertToList(Arrays.asList(ExpoAudienceConstants.position)));
        vo.setProductsList(convertToList(Arrays.asList(ExpoAudienceConstants.products)));
        vo.setProcurementNeedsList(convertToList(Arrays.asList(ExpoAudienceConstants.procurementNeeds)));
        vo.setProcurementRoleList(convertToList(Arrays.asList(ExpoAudienceConstants.procurementRole)));
        vo.setProcurementTimelineList(convertToList(Arrays.asList(ExpoAudienceConstants.procurementTimeline)));
        vo.setRequirementsList(convertToList(Arrays.asList(ExpoAudienceConstants.requirements)));
        vo.setContactTypeList(convertToList(Arrays.asList(ExpoAudienceConstants.contact)));
        return Result.build(vo);
    }

    /**
     * 预约设置
     *
     * @param dto 参数
     */
    @Override
    public void appointSet(ExpoAudienceDTO dto) {
        ExpoAudience expoAudience = expoAudienceManager.queryAudienceById(dto.getId(), dto.getExpoId());
        if (Objects.isNull(expoAudience)) {
            throw new BusinessException(-1, "观众不存在");
        }
        if (dto.getIsAppoint().equals(CommonStatus.YesOrNoEnum.YES.getValue())) {
            if (expoAudience.getIsAppoint().equals(CommonStatus.YesOrNoEnum.YES.getValue())) {
                throw new BusinessException(-1, "预约功能已开启");
            }
            // 首次开启将自己设为可预约人员 todo 加锁
            ExpoAppointedPersonnel expoAppointedPersonnel = new ExpoAppointedPersonnel();
            expoAppointedPersonnel.setExpoId(dto.getExpoId());
            expoAppointedPersonnel.setCompanyId(dto.getCompanyId());
            expoAppointedPersonnel.setMemberId(dto.getMemberId());
            expoAppointedPersonnel.setUserId(dto.getCreateUser());
            expoAppointedPersonnel.setBusinessId(expoAudience.getId());
            expoAppointedPersonnel.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
            expoAppointedPersonnelManager.save(expoAppointedPersonnel);
        } else if (dto.getIsAppoint().equals(CommonStatus.YesOrNoEnum.NO.getValue())) {
            if (expoAudience.getIsAppoint().equals(CommonStatus.YesOrNoEnum.NO.getValue())) {
                throw new BusinessException(-1, "预约功能已关闭");
            }
        } else {
            throw new BusinessException(-1, "参数错误");
        }
        // 更新
        expoAudience.setIsAppoint(dto.getIsAppoint());
        expoAudienceManager.updateById(expoAudience);
    }

    @Override
    public ExpoAudienceReportFormVO expoAudienceReportForm(ExpoAudienceDTO dto) {
        //获取到开始时间和结束时间
        DateTimeDTO dateTimeDto = DateUtil.getStartAndEndTime(dto.getDateScreeType());
        //获取观众总人数
        Integer audienceCount = expoAudienceManager.queryAllAudienceCount(dto.getCompanyId(),
                dateTimeDto.getStartTime(), dateTimeDto.getEndTime());
        //获取到到场观众信息
        dto.setStartTime(dateTimeDto.getStartTime());
        dto.setEndTime(dateTimeDto.getEndTime());
        List<Integer> audienceIdList = expoAudienceManager.queryAudienceIdList(dto);
        //进行分组 key -> 观众ID value 出现的次数
        Map<Integer, Long> audienceMap = audienceIdList.stream().
                collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        //实际到场人数
        Integer actualAttendanceNumber = Integer.valueOf(audienceMap.keySet().size());
        //过滤出现的次数超过两次的
        Map<Integer, Long> filteredMap = audienceMap.entrySet().stream()
                .filter(entry -> entry.getValue() > NumberConstant.ONE)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        //观众到场率
        BigDecimal attendanceRate = NumberConstant.ZERO != actualAttendanceNumber ? new BigDecimal(actualAttendanceNumber)
                .divide(new BigDecimal(audienceCount))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP) : new BigDecimal(NumberConstant.ZERO);
        //复访率
        BigDecimal followAudienceRate = NumberConstant.ZERO != Integer.valueOf(filteredMap.entrySet().size()) ?
                new BigDecimal(Integer.valueOf(filteredMap.entrySet().size()))
                        .divide(new BigDecimal(audienceCount))
                        .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                        .setScale(NumberConstant.TWO, RoundingMode.HALF_UP) : new BigDecimal(NumberConstant.ZERO);
        //返回对象
        ExpoAudienceReportFormVO vo = new ExpoAudienceReportFormVO();
        vo.setAudienceTotal(audienceCount);
        vo.setActualAttendanceTotal(actualAttendanceNumber);
        vo.setFollowAudienceTotal(filteredMap.entrySet().size());
        vo.setAttendanceRate(attendanceRate);
        vo.setFollowAudienceRate(followAudienceRate);
        return vo;
    }

    @Override
    public ExpoAudienceReportFormVO expoAudienceReportFormDetail(ExpoAudienceDTO dto) {
        //获取报表相关统计
        ExpoAudienceReportFormVO vo = expoAudienceReportForm(dto);
        //获取到开始时间和结束时间
        DateTimeDTO dateTimeDto = DateUtil.getStartAndEndTime(dto.getDateScreeType());
        //获取到去年同期的时间
        LocalDateTime startTime = dateTimeDto.getStartTime();
        LocalDateTime lastYearStartTime = startTime.withYear(startTime.getYear() - NumberConstant.ONE);
        LocalDateTime endTime = dateTimeDto.getEndTime();
        LocalDateTime lastYearEndTime = endTime.withYear(endTime.getYear() - NumberConstant.ONE);
        //查询去年同期观众总人数
        Integer audienceCount = expoAudienceManager.queryAllAudienceCount(dto.getCompanyId(),
                lastYearStartTime, lastYearEndTime);
        //比去年同期计算方式 （本年度所有展会总报名人数 - 去年同期所有展会总报名人数）/去年同期所有展会总报名人数
        int audienceDifferenceTotal = vo.getAudienceTotal() - audienceCount;
        //报名总数比去年同期百分比
        BigDecimal audienceDifferenceRate = new BigDecimal(audienceDifferenceTotal).divide(new BigDecimal(audienceCount))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED)).setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        vo.setAudienceDifferenceRate(audienceDifferenceRate);
        //去年同期到场率
        dto.setStartTime(lastYearStartTime);
        dto.setEndTime(lastYearEndTime);
        List<Integer> audienceIdList = expoAudienceManager.queryAudienceIdList(dto);
        //进行分组 key -> 观众ID value 出现的次数
        Map<Integer, Long> audienceMap = audienceIdList.stream().
                collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        //实际到场人数
        Integer actualAttendanceNumber = Integer.valueOf(audienceMap.keySet().size());
        //观众到场率
        BigDecimal attendanceRate = NumberConstant.ZERO != actualAttendanceNumber ? new BigDecimal(actualAttendanceNumber)
                .divide(new BigDecimal(audienceCount))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP) : new BigDecimal(NumberConstant.ZERO);
        //本年观众到场率 - 去年观众到场率
        vo.setAudienceDifferenceRate(vo.getAttendanceRate().subtract(attendanceRate));
        //未到场人数
        int noAttendanceTotal = vo.getAudienceTotal() - vo.getActualAttendanceTotal();
        vo.setNoAttendanceTotal(noAttendanceTotal);
        //未到场率
        BigDecimal noAttendanceRate = NumberConstant.ZERO != noAttendanceTotal ? new BigDecimal(noAttendanceTotal)
                .divide(new BigDecimal(vo.getAudienceTotal()))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP) : new BigDecimal(NumberConstant.ZERO);
        vo.setNoAttendanceRate(noAttendanceRate);
        //过滤出现的次数为一次的
        Map<Integer, Long> firstAudienceMap = audienceMap.entrySet().stream()
                .filter(entry -> entry.getValue().intValue() != NumberConstant.ONE)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        //首次观众总数
        Integer firstAudienceTotal = Integer.valueOf(firstAudienceMap.keySet().size());
        vo.setFirstAudienceTotal(firstAudienceTotal);
        //首次观众占比
        BigDecimal firstAudienceRate = NumberConstant.ZERO != firstAudienceTotal ? new BigDecimal(firstAudienceTotal)
                .divide(new BigDecimal(vo.getAudienceTotal()))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP) : new BigDecimal(NumberConstant.ZERO);
        vo.setFirstAudienceRate(firstAudienceRate);
        return vo;
    }

    /**
     * 获取指定列
     *
     * @param companyId
     * @param colName
     * @return
     */
    private List<ExpoAudienceExtend> listByColName(Integer companyId, String colName, Long expoId) {
        return expoAudienceExtendManager.list(new LambdaQueryWrapper<ExpoAudienceExtend>()
                .eq(ExpoAudienceExtend::getCompanyId, companyId)
                .eq(ExpoAudienceExtend::getColName, colName)
                .eq(Objects.nonNull(expoId), ExpoAudienceExtend::getExpoId, expoId)
        );
    }


    private List<ExpoCodeValueVO> convertToList(List<String> codes) {
        List<ExpoCodeValueVO> vos = new ArrayList<>();
        codes.stream().forEach(r -> {
            ExpoCodeValueVO vo = new ExpoCodeValueVO();
            vo.setCode(r);
            vo.setName(r);
            vos.add(vo);
        });
        return vos;
    }

    @Override
    public ExpoAudienceQrCodeInfoVO getQrCodeInfo(ExpoAudienceDTO dto) {
        ExpoAudienceQrCodeInfoVO vo = new ExpoAudienceQrCodeInfoVO();
        ExpoInfo expoInfo = expoInfoManager.getOneById(dto.getExpoId());
        QueryCompanyResp companyResp = feignCommonManager.selectCompanyById(expoInfo.getCompanyId());
        ExpoAudience expoAudience = expoAudienceManager.getOneById(dto.getId());
        String scanAudienceSignCode = GatewayRoutingConstants.scanAudienceSignCode();
        String qrcodeUrl = String.format(gateway + scanAudienceSignCode, dto.getId(), dto.getCompanyId(), dto.getExpoId());
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
        vo.setQrcodeUrl(qrcodeUrl);
        vo.setQrcodeImage(png);
        vo.setExpoName(expoInfo.getExpoName());
        vo.setHallName(expoInfo.getHallName());
        vo.setStartTime(expoInfo.getStartTime());
        vo.setEndTime(expoInfo.getEndTime());
        vo.setName(expoAudience.getName());
        vo.setCompanyName(expoAudience.getCompanyName());
        vo.setOrganizerCompanyName(companyResp.getCompanyName());
        return vo;
    }

    @Override
    public ExpoQrCodeInfoVO getChannelRegisterQr(ExpoAudienceDTO dto) {
        ExpoChannel expoChannel = expoChannelManager.getId(dto.getChannelId());
        ExpoForm expoForm = getAudienceRegisterForm(dto.getExpoId());
        String publishTenantId = expoForm.getPublishTenantId();
        // 获取站点的域名
        TenantInfoResp tenantInfoResp = feignCommonManager.getTenantInfoOne(publishTenantId);
        // 参数进行编码
        String urlParam = null;
        try {
            urlParam = URLEncoder.encode(String.format(GatewayRoutingConstants.scanAudienceRegisterUrlParam,
                    expoForm.getExpoId(), expoChannel.getId(), dto.getCompanyId()),
                    "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String qrcodeUrl = tenantInfoResp.getDomain() + GatewayRoutingConstants.scanAudienceRegisterUrl + urlParam;
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
        ExpoQrCodeInfoVO vo = new ExpoQrCodeInfoVO();
        vo.setQrcodeUrl(qrcodeUrl);
        vo.setQrcodeImage(png);
        return vo;
    }

    @Override
    public Boolean checkRegister(ExpoAudienceDTO dto) {
        ExpoAudience expoAudience = expoAudienceManager.queryExistAudience(dto.getExpoId(), dto.getEmail());
        return null == expoAudience;
    }

    /**
     * 分页查询观众详情列表
     *
     * @param
     * @return
     */
    @Override
    public IPage<ExpoAudienceDetailVO> expoAudienceDetail(ExpoAudienceDTO dto) {
        IPage<ExpoAudienceDetailVO> pageVO = new Page<>();
        // 获取到开始时间和结束时间
        DateTimeDTO dateTimeDto = DateUtil.getStartAndEndTime(dto.getDateScreeType());
        dto.setStartTime(dateTimeDto.getStartTime());
        dto.setEndTime(dateTimeDto.getEndTime());
        // 根据时间范围查询观众详情
        Page<ExpoAudience> expoAudienceByPage = expoAudienceManager.pageAudienceDetail(dto);
        pageVO.setTotal(expoAudienceByPage.getTotal());
        List<ExpoAudienceDetailVO> expoAudienceDetailVOS = null;
        if (!CollectionUtils.isEmpty(expoAudienceByPage.getRecords())) {
            expoAudienceDetailVOS = CopyObjectUtils.copyAlistToBlist(expoAudienceByPage.getRecords(), ExpoAudienceDetailVO.class);
            // 获取观众详情
            List<ExpoAudienceExt> audienceList = expoAudienceManager.querylist(dto);
            if (!CollectionUtils.isEmpty(audienceList)) {
                Map<Integer, ExpoAudienceExt> audienceListMap = audienceList.stream().filter(ext -> ext.getAudienceId() != null)
                        .collect(Collectors.toMap(ExpoAudienceExt::getAudienceId, Function.identity()));
                for (ExpoAudienceDetailVO expoAudienceDetailVO : expoAudienceDetailVOS) {
                    ExpoAudienceExt audienceExt = audienceListMap.get(expoAudienceDetailVO.getId());
                    if (audienceExt != null) {
                        Long visitCount = Long.valueOf(audienceExt.getTotal());
                        expoAudienceDetailVO.setVisitCount(Math.toIntExact(visitCount));
                        expoAudienceDetailVO.setStatus(visitCount > NumberConstant.ONE ? "复访观众" : "首次访问");
                        expoAudienceDetailVO.setLatestVisitTime(String.valueOf(audienceExt.getRecentlyJoinTime()));
                    }
                }
            }
        }
        pageVO.setRecords(expoAudienceDetailVOS);
        return pageVO;
    }
}
