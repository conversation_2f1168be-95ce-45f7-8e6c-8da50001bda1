package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.page.PageVO;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.ext.*;
import com.echronos.expo.service.ExpoStatisticsService;
import com.echronos.expo.vo.statistics.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-15 15:15
 */
@Slf4j
@Service
public class ExpoStatisticsServiceImpl implements ExpoStatisticsService {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoBoothManager expoBoothManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Override
    public ExpoIndexCountVO getIndexExpoCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoInfoManager.getExpoIndexCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexExhibitorCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoExhibitorManager.getIndexExhibitorCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexAudienceCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoAudienceManager.getIndexAudienceCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexBoothCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoBoothManager.getIndexBoothCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalOne().divide(indexCountExt.getTotal()).multiply(BigDecimal.valueOf(100));
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexExhibitorAnalyzeVO getIndexExhibitorAnalyzeCount(Integer companyId) {
        ExpoIndexExhibitorAnalyzeExt ext = expoExhibitorManager.getIndexExhibitorAnalyzeCount(companyId);
        // 复展率
        BigDecimal repeatRete = BigDecimal.ZERO;
        if (ext.getRepeatTotal().compareTo(BigDecimal.ZERO) > 0) {
            repeatRete = ext.getRepeatTotal().divide(ext.getTotal(), 2, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        return ExpoIndexExhibitorAnalyzeVO.builder()
                .total(ext.getTotal())
                .firstTotal(ext.getFirstTotal())
                .repeatTotal(ext.getRepeatTotal())
                .repeatRete(repeatRete)
                .build();
    }

    @Override
    public ExpoIndexExhibitorAnalyzeFirstAndRepeatVO getIndexExhibitorAnalyzeFirstAndRepeatCount(Integer companyId) {
        ExpoIndexExhibitorAnalyzeExt ext = expoExhibitorManager.getIndexExhibitorAnalyzeCount(companyId);
        return ExpoIndexExhibitorAnalyzeFirstAndRepeatVO.builder()
                .firstTotal(ext.getFirstTotal())
                .repeatTotal(ext.getRepeatTotal())
                .build();
    }

    @Override
    public ExpoIndexExhibitorAnalyzeFrequencyVO getIndexExhibitorAnalyzeFrequencyCount(Integer companyId) {
        // 参展商总数
        ExpoIndexCountExt expoIndexCountExt = expoExhibitorManager.getIndexExhibitorCount(companyId);
        ExpoIndexExhibitorAnalyzeFrequencyExt ext = expoExhibitorManager.getIndexExhibitorAnalyzeFrequencyCount(companyId);
        BigDecimal oneFrequencyRate = BigDecimal.ZERO;
        BigDecimal twoFrequencyRate = BigDecimal.ZERO;
        BigDecimal threeFrequencyRate = BigDecimal.ZERO;
        BigDecimal fourFrequencyRate = BigDecimal.ZERO;
        if(expoIndexCountExt.getTotal().compareTo(BigDecimal.ZERO) > 0){
            // 计算频率占比
            oneFrequencyRate = ext.getOneFrequency().divide(expoIndexCountExt.getTotal(), 2, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            twoFrequencyRate = ext.getTwoFrequency().divide(expoIndexCountExt.getTotal(), 2, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            threeFrequencyRate = ext.getThreeFrequency().divide(expoIndexCountExt.getTotal(), 2, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            fourFrequencyRate = ext.getFourFrequency().divide(expoIndexCountExt.getTotal(), 2, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        return ExpoIndexExhibitorAnalyzeFrequencyVO.builder()
                .oneFrequency(ext.getOneFrequency())
                .oneFrequencyRate(oneFrequencyRate)
                .twoFrequency(ext.getTwoFrequency())
                .twoFrequencyRate(twoFrequencyRate)
                .threeFrequency(ext.getThreeFrequency())
                .threeFrequencyRate(threeFrequencyRate)
                .fourFrequency(ext.getFourFrequency())
                .fourFrequencyRate(fourFrequencyRate)
                .build();
    }

    @Override
    public Result<List<ExpoIndexExhibitorAnalyzeDetailVO>> getIndexExhibitorAnalyzeDetailList(ExpoExhibitorDTO dto) {
        Page<ExpoExhibitorDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        List<ExpoIndexExhibitorAnalyzeDetailExt> list = expoExhibitorManager.getIndexExhibitorAnalyzeDetailList(page, dto);
        List<ExpoIndexExhibitorAnalyzeDetailVO> voList = null;
        if(CollectionUtil.isNotEmpty(list)){
            voList = new ArrayList<>();
            List<Integer> customerIdList = list.stream().map(item -> item.getCustomerId()).collect(Collectors.toList());
            Map<Integer, CustomerResp> customerRespMap = feignCommonManager.getBatchCustomerByIds(customerIdList);
            for(ExpoIndexExhibitorAnalyzeDetailExt ext : list){
                ExpoIndexExhibitorAnalyzeDetailVO vo = new ExpoIndexExhibitorAnalyzeDetailVO();
                CustomerResp customerResp = customerRespMap.get(ext.getCustomerId());
                vo.setName(customerResp.getName());
                vo.setTotal(ext.getTotal());
                vo.setRecentlyJoinTime(ext.getRecentlyJoinTime());
                voList.add(vo);
            }
        }
        return Result.build(voList, page.getTotal());
    }

    @Override
    public ExpoAudienceStatisticsVO getExpoAudienceStatistics(Integer expoId) {
        Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
        Integer signInCount = expoAudienceManager.getAudienceSignInCount(expoId);
        BigDecimal signInRate = BigDecimal.valueOf(signInCount).divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        Integer accumulatedSignInCount = expoAudienceManager.getAudienceCumulativeSignInCount(expoId);
        return ExpoAudienceStatisticsVO.builder()
                .audienceCount(audienceCount)
                .signInCount(signInCount)
                .signInRate(signInRate)
                .accumulatedSignInCount(accumulatedSignInCount)
                .build();
    }

    @Override
    public ExpoAudienceSignNotSignInRateVO getExpoAudienceSignInNotSignInCount(Integer expoId) {
        Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
        Integer signInCount = expoAudienceManager.getAudienceSignInCount(expoId);
        BigDecimal signInRate = BigDecimal.valueOf(signInCount).divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP);
        return ExpoAudienceSignNotSignInRateVO
                .builder()
                .signInRate(signInRate)
                .notSignInRate(new BigDecimal("100").subtract(signInRate))
                .build();
    }

    @Override
    public List<ExpoAudienceChannelVO> getExpoAudienceChannelList(Integer expoId) {
        List<ExpoAudienceChannelVO> voList = new ArrayList<>();
        List<ExpoChannelAudienceExt> channelAudienceCountList = expoAudienceManager.getChannelAudienceCountList(expoId);
        if(CollectionUtil.isNotEmpty(channelAudienceCountList)){
            // 总观众人数
            Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
            channelAudienceCountList.forEach(cac -> {
                ExpoAudienceChannelVO vo = new ExpoAudienceChannelVO();
                // 签到率：渠道签到人数/渠道观众人数
                BigDecimal signInRate = new BigDecimal(cac.getSignInCount())
                        .divide(BigDecimal.valueOf(cac.getRegisterCount()), 2, BigDecimal.ROUND_HALF_UP);
                // 注册比率：渠道观众人数/总人数
                BigDecimal registerRate = new BigDecimal(cac.getRegisterCount())
                        .divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP);
                vo.setChannelName(cac.getChannelName());
                vo.setRegisterCount(cac.getRegisterCount());
                vo.setRegisterRate(registerRate);
                vo.setSignInRate(signInRate);
            });
        }
        return voList;
    }

    @Override
    public List<ExpoAudienceDateCountVO> getAudienceSingInDayCount(Integer expoId) {
        List<ExpoAudienceDateCountExt> list = expoAudienceManager.getAudienceSingInDayCount(expoId);
        List<ExpoAudienceDateCountVO> voList = null;
        if(CollectionUtil.isNotEmpty(list)){
            voList = CopyObjectUtils.copyAlistToBlist(list, ExpoAudienceDateCountVO.class);
        }
        return voList;
    }

    @Override
    public List<ExpoAudienceDateCountVO> getAudienceSingInTimeCount(Integer expoId, LocalDateTime time) {
        List<ExpoAudienceDateCountExt> list = expoAudienceManager.getAudienceSingInTimeCount(expoId, time);
        List<ExpoAudienceDateCountVO> voList = null;
        if(CollectionUtil.isNotEmpty(list)){
            voList = CopyObjectUtils.copyAlistToBlist(list, ExpoAudienceDateCountVO.class);
        }
        return voList;
    }

}
