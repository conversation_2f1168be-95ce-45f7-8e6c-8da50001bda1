package com.echronos.expo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.commons.Result;
import com.echronos.commons.model.RequestUser;
import com.echronos.expo.dto.*;
import com.echronos.expo.param.ExpoAudienceImportParam;
import com.echronos.expo.param.ExpoAudienceListFilterExportParam;
import com.echronos.expo.param.ExpoAudienceListFilterParam;
import com.echronos.expo.param.ExpoAudiencePrintSignParam;
import com.echronos.expo.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15 17:27
 * @ClassName IExpoAudienceService
 */
public interface IExpoAudienceService {

    /**
     * 分页查询观众
     *
     * @param param
     * @return
     */
    Result<List<ExpoAudienceVO>> pageFor(ExpoAudienceListFilterParam param);

    /**
     * 分页查询观众列表
     * @param dto
     * @return
     */
    Result<List<ExpoAudienceVO>> pageList(ExpoAudiencePageDTO dto);

    /**
     * 观众打印签到列表
     * @param dto
     * @return
     */
    Result<List<AudiencePrintSignVO>> printSignPageList(ExpoAudiencePageDTO dto);

    /**
     * 添加打印次数
     * @param dto
     */
    void addPrintFrequency(ExpoAudienceDTO dto);

    /**
     * 查询观众列表
     *
     * @param param
     * @return
     */
    List<ExpoAudienceDTO> list(ExpoAudienceListFilterExportParam param);

    /**
     * 查询观众列表
     *
     * @param dto
     * @return
     */
    Result<ExpoAudienceVO> info(ExpoAudienceDTO dto);

    /**
     * 查询观众列表
     *
     * @param dto
     * @return
     */
    Result<List<ExpoAudienceVO>> list(ExpoAudienceListDTO dto);

    /**
     * 观众扫码加载表单信息
     *
     * @param dto
     * @return
     */
    Result<ExpoFormVO> scanRegisterForm(ExpoFormDTO dto);

    /**
     * 观众登记
     *
     * @param dto
     * @return
     */
    Result<ExpoAudienceScanVO> register(ExpoAudienceDTO dto);

    /**
     * 观众注册提交
     * @param dto
     * @return
     */
    ExpoAudienceScanVO registerSubmit(ExpoAudienceDTO dto);

    /**
     * 编辑观众信息
     *
     * @param dto
     * @return
     */
    Result edit(ExpoAudienceDTO dto);

    /**
     * 删除观众信息
     *
     * @param dto
     * @return
     */
    Result del(ExpoAudienceDTO dto);

    /**
     * 观众签到
     *
     * @param dto
     * @return
     */
    Result<ExpoAudienceVO> sign(ExpoAudienceDTO dto);

    /**
     * 发送邮件
     *
     * @param dto
     * @return
     */
    void sendEmail(ExpoAudienceEmailRecordsDTO dto);

    /**
     * 观众模板下载
     *
     * @param response http
     * @param user     当前用户
     */
    void downLoadTemplateExcel(HttpServletResponse response, RequestUser user);

    /**
     * 观众数据导出
     * @param expoId 展会ID
     * @param response 输出流
     * @param user 用户信息
     * @param valueList 数据列表
     */
    void exportData(Integer expoId, HttpServletResponse response, RequestUser user,  List<List<String>> valueList);

    /**
     * 观众导入数据检查
     *
     * @param param
     * @param requestUser
     * @return
     */
    AudienceImportResultVO checkImportExcel(ExpoAudienceImportParam param, RequestUser requestUser);

    /**
     * 观众导入数据检查
     *
     * @param param
     * @param requestUser
     * @return
     */
    AudienceImportResultVO checkImportExcelV2(ExpoAudienceImportParam param, RequestUser requestUser);

    /**
     * 观众导入数据
     *
     * @param param
     * @param requestUser
     * @return
     */
    AudienceImportResultVO importExel(ExpoAudienceImportParam param, RequestUser requestUser);

    /**
     * 观众列表导出
     *
     * @param response
     * @param param
     * @param user
     */
    void exportExcel(HttpServletResponse response, ExpoAudienceListFilterExportParam param, RequestUser user);


    void syncCrm();


    /**
     * 根据列名获取字典列表
     *
     * @param colName
     * @param companyId
     * @param expoId
     * @return
     */
    Result<List<ExpoCodeValueVO>> dictByColName(String colName, Integer companyId, Long expoId);


    /**
     * 字典列表
     *
     * @return
     */
    Result<ExpoDictCodeValueVO> dictList();

    /**
     * 预约设置
     *
     * @param dto 参数
     */
    void appointSet(ExpoAudienceDTO dto);

    /**
     * 观众分析报表统计
     *
     * @param dto
     */
    ExpoAudienceReportFormVO expoAudienceReportForm(ExpoAudienceDTO dto);

    /**
     * 观众分析报表统计详情
     * @param dto
     * @return
     */
    ExpoAudienceReportFormVO expoAudienceReportFormDetail(ExpoAudienceDTO dto);
    /**
     * 获取观众二维码信息
     * @return
     */
    ExpoAudienceQrCodeInfoVO getQrCodeInfo(ExpoAudienceDTO dto);

    /**
     * 获取渠道观众注册二维码
     * @param dto
     * @return
     */
    ExpoQrCodeInfoVO getChannelRegisterQr(ExpoAudienceDTO dto);

    /**
     * 校验是否已经注册
     * @param dto
     * @return
     */
    Boolean checkRegister(ExpoAudienceDTO dto);

    /**
     * 分页查询详情列表
     * @param dto
     * @return
     */
    IPage<ExpoAudienceDetailVO> expoAudienceDetail(ExpoAudienceDTO dto);

}
