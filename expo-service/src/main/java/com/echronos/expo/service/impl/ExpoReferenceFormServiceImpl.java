package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.DateUtils;
import com.echronos.expo.dto.ExpoReferenceFormDTO;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.enums.ExpoReferenceFormEnum;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.enums.PermissionEnum;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.service.IExpoReferenceFormService;
import com.echronos.expo.vo.ExpoReferenceFormExtendVO;
import com.echronos.expo.vo.ExpoReferenceFormVO;
import com.echronos.nms.api.enums.NmsMsgTypeEnums;
import com.echronos.nms.api.enums.NmsTemplateEnums;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025-08-09 11:31
 */
@Slf4j
@Service
public class ExpoReferenceFormServiceImpl implements IExpoReferenceFormService {

    @Resource
    private ExpoReferenceFormManager expoReferenceFormManager;
    @Resource
    private ExpoReferenceFormExtendManager expoReferenceFormExtendManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Override
    public ExpoReferenceFormVO getDetailById(ExpoReferenceFormDTO dto) {
        ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
        if(null == referenceForm){
            throw new BusinessException(-1, "表单不存在");
        }
        List<ExpoReferenceFormExtend> referenceFormExtendList = expoReferenceFormExtendManager.getListByRefFormId(dto.getId());
        List<ExpoReferenceFormExtendVO> extendVOList = null;
        if(CollectionUtil.isNotEmpty(referenceFormExtendList)){
            extendVOList = CopyObjectUtils.copyAlistToBlist(referenceFormExtendList, ExpoReferenceFormExtendVO.class);
        }
        ExpoReferenceFormVO referenceFormVO = CopyObjectUtils.copyAtoB(referenceForm, ExpoReferenceFormVO.class);
        referenceFormVO.setExtendList(extendVOList);
        return referenceFormVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(ExpoReferenceFormDTO dto) {
        ExpoForm expoForm = expoFormManager.getFormByFormCode(dto.getFormCode());
        if(null == expoForm){
            // 表单不存在未绑定
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_EXIST_OR_UNBOUND.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_EXIST_OR_UNBOUND.getMessage());
        }else if(CommonStatus.StatusEnum.DISABLE.getValue() == expoForm.getIsEnable()){
            // 表单未启用
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getMessage());
        }
        if(null == dto.getId()){
            ExpoReferenceForm isExist = expoReferenceFormManager.getByGroupTypeBusinessId(dto.getBusinessId(),
                    expoForm.getFormGroup(), expoForm.getFormType());
            if(null != isExist){
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_EXISTS.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_EXISTS.getMessage());
            }
        }else{
            ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
            if(null == referenceForm){
                // 表单不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
            }
            if(referenceForm.getExpoId() != expoForm.getExpoId()){
                // 表单不属于次展会
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_BELONG_TO_EXPO.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_BELONG_TO_EXPO.getMessage());
            }
        }
        dto.setAuditStatus(ExpoReferenceFormEnum.AuditStatus.WAIT_AUDIT.getCode());
        if(ExpoFormEnums.FormGroup.AUDIENCE.getCode() == expoForm.getFormGroup()){
            ExpoAudience isExist = expoAudienceManager.queryAudienceById(dto.getBusinessId(), null);
            if(null == isExist){
                // 观众不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NOT_EXIST.getMessage());
            }
            // 观众表单默认审核通过
            dto.setAuditStatus(ExpoReferenceFormEnum.AuditStatus.AUDIT_PASS.getCode());
        } else {
            ExpoExhibitor isExist = expoExhibitorManager.getByExhibitorId(dto.getBusinessId());
            if(null == isExist){
                // 展商不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_NOT_EXIST.getMessage());
            }
        }
        dto.setExpoId(expoForm.getExpoId());
        LocalDateTime submitTime = LocalDateTime.now();
        dto.setSubmitTime(submitTime);
        expoReferenceFormManager.saveOrUpdate(dto);
        expoReferenceFormExtendManager.removeByRefFormId(dto.getId());
        List<ExpoReferenceFormExtend> extendList = CopyObjectUtils.copyAlistToBlist(dto.getExtendList(), ExpoReferenceFormExtend.class);
        expoReferenceFormExtendManager.saveBatch(extendList);
        // 更新展商表单审核状态
        expoExhibitorManager.updateExhibitorAuditStatus(dto.getId(), dto.getAuditStatus(), expoForm.getFormType());

        // 推送消息
        if(ExpoFormEnums.FormGroup.EXHIBITOR.getCode() == expoForm.getFormGroup() &&
                ExpoReferenceFormEnum.AuditStatus.WAIT_AUDIT.getCode() == dto.getAuditStatus()){
            List<Integer> receiveUserIds = feignCommonManager.queryPermCodeMember(expoForm.getCompanyId(), PermissionEnum.EXHIBITOR_QUERY_DETAIL.getCode());
            if(CollectionUtil.isNotEmpty(receiveUserIds)){
                QueryCompanyResp companyResp = feignCommonManager.selectCompanyById(dto.getCompanyId());
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("companyId", expoForm.getCompanyId());
                paramMap.put("expoId", expoForm.getExpoId());
                paramMap.put("expoExhibitorId",dto.getBusinessId());
                paramMap.put("formName",expoForm.getFormName());
                paramMap.put("companyName", companyResp.getCompanyName());
                // 获取请求时区时间
                TimeZone timeZone = LocaleContextHolder.getTimeZone();
                String submitTimeText = submitTime.atZone(timeZone.toZoneId()).format(DateTimeFormatter.ofPattern(DateUtils.FMT_LONG_DATE));
                paramMap.put("commitTime", submitTimeText);
                feignCommonManager.sendImMsg(receiveUserIds, expoForm.getCompanyId(),
                        dto.getUserId(), paramMap,
                        NmsMsgTypeEnums.IM_EXPO_NOTICE, NmsTemplateEnums.EXPO_EXHIBITOR_INFORMATION_CHECK_NOTICE);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(ExpoReferenceFormDTO dto) {
        ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
        if(null == referenceForm){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getMessage());
        }else if(ExpoReferenceFormEnum.AuditStatus.WAIT_AUDIT.getCode() != referenceForm.getAuditStatus()){
            // 当前表单已审核
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_REVIEWED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_REVIEWED.getMessage());
        }
        ExpoInfo expoInfo = expoInfoManager.getById(referenceForm.getExpoId());
        if( expoInfo.getCompanyId() != dto.getCompanyId()){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_DATA_NO_PERMISSION.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_DATA_NO_PERMISSION.getMessage());
        }
        ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(referenceForm.getBusinessId());

        ExpoReferenceForm update = new ExpoReferenceForm();
        update.setId(dto.getId());
        update.setAuditStatus(dto.getAuditStatus());
        update.setAuditRemark(dto.getAuditRemark());
        LocalDateTime auditTime = LocalDateTime.now();
        update.setAuditTime(auditTime);
        expoReferenceFormManager.updateById(update);

        // 更新展商表单审核状态
        ExpoForm expoForm = expoFormManager.getId(referenceForm.getFormId());
        expoExhibitorManager.updateExhibitorAuditStatus(referenceForm.getBusinessId(), dto.getAuditStatus(), expoForm.getFormType());

        // 推送消息
        List<Integer> receiveUserIds = feignCommonManager.queryPermCodeMember(expoExhibitor.getCustomerCompanyId(), PermissionEnum.EXHIBITOR_SUBMIT.getCode());
        if(CollectionUtil.isNotEmpty(receiveUserIds)){
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("companyId", expoExhibitor.getCustomerCompanyId());
            paramMap.put("expoId", expoExhibitor.getExpoId());
            paramMap.put("expoExhibitorId",expoExhibitor.getId());
            paramMap.put("formName",expoForm.getFormName());
            paramMap.put("statusName", ExpoReferenceFormEnum.AuditStatus.getEnumByCode(dto.getAuditStatus()).getMsg());
            paramMap.put("auditTime",auditTime);
            NmsTemplateEnums expoExhibitorInformationCheckRejectNotice = null;
            if(ExpoReferenceFormEnum.AuditStatus.AUDIT_PASS.getCode() == dto.getAuditStatus()){
                // 审核通过
                expoExhibitorInformationCheckRejectNotice = NmsTemplateEnums.EXPO_EXHIBITOR_INFORMATION_CHECK_PASS_NOTICE;
            }else if(ExpoReferenceFormEnum.AuditStatus.AUDIT_REJECT.getCode() == dto.getAuditStatus()){
                // 审核拒绝
                paramMap.put("rejectReason",dto.getAuditRemark());
                expoExhibitorInformationCheckRejectNotice = NmsTemplateEnums.EXPO_EXHIBITOR_INFORMATION_CHECK_REJECT_NOTICE;
            }
            feignCommonManager.sendImMsg(receiveUserIds, expoExhibitor.getCustomerCompanyId(),
                    dto.getUserId(), paramMap,
                    NmsMsgTypeEnums.IM_EXPO_NOTICE, expoExhibitorInformationCheckRejectNotice);
        }
    }

}
