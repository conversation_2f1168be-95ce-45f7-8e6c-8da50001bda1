package com.echronos.expo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.page.PageVO;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.ExpoAppointPurposeEnum;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.model.ext.ExpoAppointedPersonnelTimeExt;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import com.echronos.expo.service.IExpoAppointService;
import com.echronos.expo.vo.ExpoAttachmentFileVO;
import com.echronos.expo.vo.ExpoBoothDetailVO;
import com.echronos.expo.vo.appoint.*;
import com.echronos.system.resp.MemberResp;
import com.echronos.user.api.resp.UserInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.echronos.user.api.resp.member.BatchMemberInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/7 11:20
 */
@Service
@Slf4j
public class ExpoAppointServiceImpl implements IExpoAppointService {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAppointedPersonnelManager expoAppointedPersonnelManager;
    @Resource
    private ExpoAppointedPersonnelTimeManager expoAppointedPersonnelTimeManager;
    @Resource
    private ExpoAppointmentManager expoAppointmentManager;
    @Resource
    private ExpoExhibitorBoothManager expoExhibitorBoothManager;
    @Resource
    private ExpoAttachmentFileManager expoAttachmentFileManager;
    @Resource
    private ExpoAppointmentTimeManager expoAppointmentTimeManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    /**
     * 查询预约设置
     *
     * @param dto 参数
     * @return AppointmentSetVO
     */
    @Override
    public AppointmentSetVO getAppointSet(ExpoAppointedPersonnelDTO dto) {
        // 查询展会
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(-1, "展会不存在");
        }
        // 是否开启预约功能
        Integer isAppoint = 0;
        // 查询观众
        if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
            ExpoAudience expoAudience = expoAudienceManager.queryAudienceById(dto.getBusinessId(), dto.getExpoId());
            if (Objects.isNull(expoAudience)) {
                throw new BusinessException(-1, "观众不存在");
            }
            isAppoint = expoAudience.getIsAppoint();
        }
        // 查询展商
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(dto.getBusinessId());
            if (Objects.isNull(expoExhibitor) || !expoExhibitor.getExpoId().equals(dto.getExpoId())) {
                throw new BusinessException(-1, "展商不存在");
            }
            isAppoint = expoExhibitor.getIsAppoint();
        }
        AppointmentSetVO vo = new AppointmentSetVO();
        vo.setId(dto.getBusinessId());
        vo.setExpoId(dto.getExpoId());
        vo.setIsAppoint(isAppoint);
        // 开启了预约功能
        if (1 == isAppoint) {
            vo.setExpoStartTime(expoInfo.getStartTime());
            vo.setExpoEndTime(expoInfo.getEndTime());
            // 查询已设置的可预约人员
            List<ExpoAppointedPersonnel> personnelList = expoAppointedPersonnelManager.queryList(dto);
            if (!CollectionUtils.isEmpty(personnelList)) {
                List<ExpoAppointedPersonnelVO> personnelVOS = CopyObjectUtils.copyAlistToBlist(personnelList, ExpoAppointedPersonnelVO.class);
                // 成员信息
                Map<Integer, BatchMemberInfoResp> memberMap = new HashMap<>();
                // 观众信息
                Map<Integer, ExpoAudience> audienceMap = new HashMap<>();
                // 查询
                if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                    List<Integer> audienceIds = personnelVOS.stream().map(ExpoAppointedPersonnelVO::getBusinessId).distinct().collect(Collectors.toList());
                    List<ExpoAudience> expoAudiences = expoAudienceManager.queryByIds(audienceIds);
                    if (!CollectionUtils.isEmpty(expoAudiences)) {
                        audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getId, v -> v));
                    }
                } else if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                    List<Integer> memberIds = personnelVOS.stream().map(ExpoAppointedPersonnelVO::getMemberId).collect(Collectors.toList());
                    memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
                }
                // 组装
                for (ExpoAppointedPersonnelVO personnelVO : personnelVOS) {
                    if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                        personnelVO.setMemberName(audienceMap.getOrDefault(personnelVO.getBusinessId(), new ExpoAudience()).getName());
                    }
                    if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                        BatchMemberInfoResp memberInfoResp = memberMap.get(personnelVO.getMemberId());
                        if (Objects.nonNull(memberInfoResp)) {
                            personnelVO.setMemberName(memberInfoResp.getName());
                            personnelVO.setPosition(memberInfoResp.getPosition());
                        }
                    }
                    personnelVO.setTimeSize(0);
                    if (!CollectionUtils.isEmpty(personnelVO.getTimeList())) {
                        personnelVO.setTimeSize(personnelVO.getTimeList().size());
                    }
                }
                vo.setPersonnelList(personnelVOS);
            }
        }
        return vo;
    }

    /**
     * 设置时间
     *
     * @param dto 参数
     */
    @Override
    public void appointSetTime(ExpoAppointedPersonnelDTO dto) {
        ExpoAppointedPersonnel expoAppointedPersonnel = expoAppointedPersonnelManager.queryOne(dto);
        if (Objects.isNull(expoAppointedPersonnel)) {
            throw new BusinessException(-1, "预约人员不存在");
        }
        List<ExpoAppointedPersonnelTime> timeList = dto.getTimeList();
        Map<String, ExpoAppointedPersonnelTime> uniqueMap = new LinkedHashMap<>();
        // 是否存在时间段
        boolean isTime = false;
        if (!CollectionUtils.isEmpty(timeList)) {
            isTime = true;
            // 去重（时间段一样的）
            uniqueMap = timeList.stream()
                    .collect(Collectors.toMap(
                            slot -> slot.getStartTime() + "_" + slot.getEndTime(),
                            slot -> slot,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));
            timeList = new ArrayList<>(uniqueMap.values());
            for (ExpoAppointedPersonnelTime personnelTime : timeList) {
                if (!personnelTime.getStartTime().isBefore(personnelTime.getEndTime())) {
                    throw new BusinessException(-1, "开始时间不能大于结束时间");
                }
                personnelTime.setPersonnelId(expoAppointedPersonnel.getId());
            }
            // 按startTime排序（若startTime相同，按endTime排序）
            timeList.sort(Comparator
                    .comparing(ExpoAppointedPersonnelTime::getStartTime)
                    .thenComparing(ExpoAppointedPersonnelTime::getEndTime));
            for (int i = 0; i < timeList.size() - 1; i++) {
                ExpoAppointedPersonnelTime current = timeList.get(i);
                ExpoAppointedPersonnelTime next = timeList.get(i + 1);
                // 当前结束时间 > 下一个开始时间 → 存在重叠
                if (current.getEndTime().isAfter(next.getStartTime())) {
                    throw new BusinessException(-1, "时间段有重叠");
                }
            }
            // 查询展会
            ExpoInfo expoInfo = expoInfoManager.getById(expoAppointedPersonnel.getExpoId());
            if (Objects.isNull(expoInfo)) {
                throw new BusinessException(-1, "展会不存在");
            }
            if (timeList.get(0).getStartTime().isBefore(expoInfo.getStartTime()) || timeList.get(timeList.size() - 1).getEndTime().isAfter(expoInfo.getEndTime())) {
                throw new BusinessException(-1, "时间段超出展会时间范围");
            }
        }
        // 查询已设置的时间段（包含是否被预约标识）
        List<ExpoAppointedPersonnelTimeExt> oldTimeList = expoAppointedPersonnelTimeManager.queryBy(dto.getId());
        if (CollectionUtils.isEmpty(oldTimeList)) {
            // 没有已设置的时间段，直接保存
            if (isTime) {
                expoAppointedPersonnelTimeManager.saveBatch(timeList);
            }
        } else {
            if (isTime) {
                // 要删除的时间段
                List<Integer> delIds = new ArrayList<>();
                for (ExpoAppointedPersonnelTimeExt personnelTimeExt : oldTimeList) {
                    String key = personnelTimeExt.getStartTime() + "_" + personnelTimeExt.getEndTime();
                    ExpoAppointedPersonnelTime personnelTime = uniqueMap.get(key);
                    if (Objects.isNull(personnelTime)) {
                        if (personnelTimeExt.getIsAvailable() == 0) {
                            throw new BusinessException(-1, "存在已被预约的时间段不可移除");
                        }
                        delIds.add(personnelTimeExt.getId());
                    } else {
                        uniqueMap.remove(key);
                    }
                }
                if (!CollectionUtils.isEmpty(delIds)) {
                    expoAppointedPersonnelTimeManager.delById(delIds);
                }
                // 要添加的时间段
                if (!CollectionUtils.isEmpty(uniqueMap)) {
                    List<ExpoAppointedPersonnelTime> addTimeList = new ArrayList<>(uniqueMap.values());
                    addTimeList.forEach(v -> v.setPersonnelId(expoAppointedPersonnel.getId()));
                    expoAppointedPersonnelTimeManager.saveBatch(addTimeList);
                }
            } else {
                boolean match = oldTimeList.stream().anyMatch(v -> v.getIsAvailable() == 0);
                if (match) {
                    throw new BusinessException(-1, "存在已被预约的时间段不可移除");
                }
                // 删除已设置的时间段
                expoAppointedPersonnelTimeManager.delById(oldTimeList.stream().map(ExpoAppointedPersonnelTimeExt::getId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 可预约人员回显
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnelVO>
     */
    @Override
    public List<ExpoAppointedPersonnelVO> appointPersonnelEcho(ExpoAppointedPersonnelDTO dto) {
        List<ExpoAppointedPersonnel> list = expoAppointedPersonnelManager.queryBy(dto);
        if (!CollectionUtils.isEmpty(list)) {
            List<ExpoAppointedPersonnelVO> personnelVOS = CopyObjectUtils.copyAlistToBlist(list, ExpoAppointedPersonnelVO.class);
            // 查询成员信息
            List<Integer> memberIds = personnelVOS.stream().map(ExpoAppointedPersonnelVO::getMemberId).collect(Collectors.toList());
            Map<Integer, BatchMemberInfoResp> map = feignCommonManager.queryMemberDetailsByIds(memberIds);
            // 组装
            for (ExpoAppointedPersonnelVO personnelVO : personnelVOS) {
                BatchMemberInfoResp memberInfoResp = map.get(personnelVO.getMemberId());
                if (Objects.nonNull(memberInfoResp)) {
                    personnelVO.setMemberName(memberInfoResp.getName());
                }
            }
            return personnelVOS;
        }
        return null;
    }

    /**
     * 添加可预约人员
     *
     * @param dto 参数
     */
    @Override
    public void appointAddPersonnel(ExpoAppointedPersonnelDTO dto) {
        // 查询展会
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(-1, "展会不存在");
        }
        // 查询展商
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(dto.getBusinessId());
            if (Objects.isNull(expoExhibitor)) {
                throw new BusinessException(-1, "展商不存在");
            }
        }
        List<Integer> memberIds = dto.getMemberIds();
        // 查询已添加
        List<ExpoAppointedPersonnel> list = expoAppointedPersonnelManager.queryBy(dto);
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> ids = list.stream().map(ExpoAppointedPersonnel::getMemberId).collect(Collectors.toList());
            memberIds = memberIds.stream().filter(v -> !ids.contains(v)).collect(Collectors.toList());
        }
        // 添加
        if (!CollectionUtils.isEmpty(memberIds)) {
            // 查询成员
            Map<Integer, BatchMemberInfoResp> memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
            List<ExpoAppointedPersonnel> addList = new ArrayList<>();
            for (Integer memberId : memberIds) {
                ExpoAppointedPersonnel expoAppointedPersonnel = new ExpoAppointedPersonnel();
                expoAppointedPersonnel.setExpoId(dto.getExpoId());
                expoAppointedPersonnel.setMemberId(memberId);
                expoAppointedPersonnel.setBusinessType(dto.getBusinessType());
                expoAppointedPersonnel.setBusinessId(dto.getBusinessId());
                expoAppointedPersonnel.setCompanyId(dto.getCompanyId());
                expoAppointedPersonnel.setUserId(memberMap.getOrDefault(memberId, new BatchMemberInfoResp()).getUserId());
                addList.add(expoAppointedPersonnel);
            }
            expoAppointedPersonnelManager.saveBatch(addList);
        }
    }

    /**
     * 删除可预约人员
     *
     * @param dto 参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void appointDelPersonnel(ExpoAppointedPersonnelDTO dto) {
        ExpoAppointedPersonnel expoAppointedPersonnel = expoAppointedPersonnelManager.queryOne(dto);
        if (Objects.isNull(expoAppointedPersonnel)) {
            throw new BusinessException(-1, "预约人员不存在");
        }
        // 查询被预约列表
        List<ExpoAppointment> appointments = expoAppointmentManager.queryAppointedByPersonnelId(dto.getId());
        if (!CollectionUtils.isEmpty(appointments)) {
            throw new BusinessException(-1, "该人员已被预约，不可删除");
        }
        // 删除预约人员
        expoAppointedPersonnelManager.delById(dto.getId());
        // 删除预约人员时间段
        expoAppointedPersonnelTimeManager.delByPersonnelId(dto.getId());
    }

    /**
     * 查询可预约人员
     *
     * @param dto 参数
     * @return List<AppointedPersonnelListVO>
     */
    @Override
    public List<AppointedPersonnelListVO> personnelList(ExpoAppointedPersonnelDTO dto) {
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            if (null == dto.getBusinessId()) {
                throw new BusinessException(-1, "展商ID不能为空");
            }
        } else {
            dto.setBusinessId(null);
        }
        // 查询
        List<ExpoAppointedPersonnel> list = expoAppointedPersonnelManager.queryCanAppointedList(dto);
        if (!CollectionUtils.isEmpty(list)) {
            List<AppointedPersonnelListVO> personnelVOS = CopyObjectUtils.copyAlistToBlist(list, AppointedPersonnelListVO.class);
            // 成员信息
            Map<Integer, BatchMemberInfoResp> memberMap = new HashMap<>();
            // 观众信息
            Map<Integer, ExpoAudience> audienceMap = new HashMap<>();
            // 查询
            if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                List<Integer> memberIds = personnelVOS.stream().map(AppointedPersonnelListVO::getMemberId).collect(Collectors.toList());
                memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
            } else if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                List<Integer> audienceIds = personnelVOS.stream().map(AppointedPersonnelListVO::getBusinessId).distinct().collect(Collectors.toList());
                List<ExpoAudience> expoAudiences = expoAudienceManager.queryByIds(audienceIds);
                if (!CollectionUtils.isEmpty(expoAudiences)) {
                    audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getId, v -> v));
                }
            }
            // 组装
            for (AppointedPersonnelListVO personnelVO : personnelVOS) {
                if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                    BatchMemberInfoResp memberInfoResp = memberMap.get(personnelVO.getMemberId());
                    if (Objects.nonNull(memberInfoResp)) {
                        personnelVO.setMemberName(memberInfoResp.getName());
                        personnelVO.setPosition(memberInfoResp.getPosition());
                    }
                }
                if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                    personnelVO.setMemberName(audienceMap.getOrDefault(personnelVO.getBusinessId(), new ExpoAudience()).getName());
                }
            }
            return personnelVOS;
        }
        return null;
    }

    /**
     * 根据展商ID查询预约页面信息
     *
     * @param dto 参数
     * @return AppointPageInfoVO
     */
    @Override
    public AppointPageInfoVO pageInfo(ExpoExhibitorDTO dto) {
        AppointPageInfoVO vo = new AppointPageInfoVO();
        // 查询展商
        ExpoExhibitor exhibitor = expoExhibitorManager.getByExhibitorId(dto.getId());
        if (Objects.isNull(exhibitor)) {
            throw new BusinessException(-1, "展商不存在");
        }
        vo.setId(exhibitor.getId());
        // 查询展会
        ExpoInfo expoInfo = expoInfoManager.getById(exhibitor.getExpoId());
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(-1, "展会不存在");
        }
        // 设置展会信息
        vo.setExpoId(expoInfo.getId());
        vo.setAddress(expoInfo.getAddress());
        vo.setHallName(expoInfo.getHallName());
        vo.setStartTime(expoInfo.getStartTime());
        vo.setEndTime(expoInfo.getEndTime());
        // 查询展位
        List<ExpoBoothDetailVO> boothList = new ArrayList<>();
        List<ExpoExhibitorBoothDTO> boothDTOS = expoExhibitorBoothManager.getDetailByExhibitorId(exhibitor.getId());
        if (!CollectionUtils.isEmpty(boothDTOS)) {
            for (ExpoExhibitorBoothDTO boothDTO : boothDTOS) {
                ExpoBoothDetailVO detailVO = new ExpoBoothDetailVO();
                detailVO.setId(boothDTO.getBoothId());
                detailVO.setBoothName(boothDTO.getBoothName());
                detailVO.setBoothFloor(boothDTO.getBoothFloor());
                detailVO.setBoothZone(boothDTO.getBoothZone());
                detailVO.setBoothNumber(boothDTO.getBoothNumber());
                boothList.add(detailVO);
            }
        }
        vo.setBoothList(boothList);
        return vo;
    }

    /**
     * 提交预约申请
     *
     * @param dto 参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void appointSubmit(ExpoAppointmentDTO dto) {
        // 查询展会
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(-1, "展会不存在");
        }
        // 业务ID
        Integer businessId = dto.getBusinessId();
        ExpoAppointedPersonnelDTO appointedPersonnelDTO = new ExpoAppointedPersonnelDTO();
        // 查询观众 注：目前观众是个人公司，后续到人级别查询得带上userId或memberId
        if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
            // 查询当前登录用户手机号或者邮箱
            UserInfoResp userInfoResp = feignCommonManager.getUserById(dto.getCreateUser());
            if(StringUtils.isBlank(userInfoResp.getEmail()) && StringUtils.isBlank(userInfoResp.getPhone())){
                log.error("登录的观众账号手机号和邮箱都是空");
                throw new BusinessException(-1, "您暂未成为该展会的观众，不可预约");
            }
            // 根据手机号或者邮箱匹配是那个客户
            CustomerResp customerResp = feignCommonManager.getIndividualPhoneOrEmail(expoInfo.getCompanyId(), userInfoResp.getPhone(), userInfoResp.getEmail());
            if(null == customerResp){
                log.info("根据手机号或者邮箱未能查询到客户");
                throw new BusinessException(-1, "您暂未成为该展会的观众，不可预约");
            }
            ExpoAudienceDTO expoAudienceDTO = new ExpoAudienceDTO();
            expoAudienceDTO.setExpoId(dto.getExpoId());
            expoAudienceDTO.setCustomerId(customerResp.getId());
            ExpoAudience expoAudience = expoAudienceManager.queryBy(expoAudienceDTO);
            if (Objects.isNull(expoAudience)) {
                throw new BusinessException(-1, "您暂未成为该展会的观众，不可预约");
            }
            businessId = expoAudience.getId();
            dto.setBusinessId(businessId);
            appointedPersonnelDTO.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        }
        // 查询展商
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            if (null == businessId) {
                throw new BusinessException(-1, "展商ID不能为空");
            }
            ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(businessId);
            if (Objects.isNull(expoExhibitor) || !expoExhibitor.getExpoId().equals(dto.getExpoId())) {
                throw new BusinessException(-1, "您暂未成为该展会的展商，不可预约");
            }
            appointedPersonnelDTO.setBusinessType(ExpoBusinessTypeEnum.AUDIENCE.getCode());
        }
        // 查询预约人员
        appointedPersonnelDTO.setId(dto.getAppointedPersonnelId());
        appointedPersonnelDTO.setExpoId(dto.getExpoId());
        ExpoAppointedPersonnelDTO expoAppointedPersonnel = expoAppointedPersonnelManager.queryInfo(appointedPersonnelDTO);
        if (Objects.isNull(expoAppointedPersonnel)) {
            throw new BusinessException(-1, "预约人员不存在");
        }
        if (expoAppointedPersonnel.getIsAppoint() == 0) {
            throw new BusinessException(-1, "预约对象未开启预约功能");
        }
        // 查询已设置的时间段（包含是否被预约标识）
        List<ExpoAppointedPersonnelTimeExt> oldTimeList = expoAppointedPersonnelTimeManager.queryBy(dto.getAppointedPersonnelId());
        if (CollectionUtils.isEmpty(oldTimeList)) {
            throw new BusinessException(-1, "预约人员时间段不存在");
        }
        // 要保存的时间段
        List<ExpoAppointmentTime> addTimeList = new ArrayList<>();
        // 校验时间段
        Map<Integer, ExpoAppointedPersonnelTimeExt> timeExtMap = oldTimeList.stream().collect(Collectors.toMap(ExpoAppointedPersonnelTime::getId, v -> v));
        for (Integer timeId : dto.getTimeIds()) {
            ExpoAppointedPersonnelTimeExt timeExt = timeExtMap.get(timeId);
            if (Objects.isNull(timeExt)) {
                throw new BusinessException(-1, "预约人员时间段不存在");
            }
            if (timeExt.getIsAvailable() == 0) {
                throw new BusinessException(-1, "存在已被预约的时间段");
            }
            ExpoAppointmentTime time = new ExpoAppointmentTime();
            time.setAppointedTimeId(timeId);
            time.setStartTime(timeExt.getStartTime());
            time.setEndTime(timeExt.getEndTime());
            addTimeList.add(time);
        }
        // 按startTime升序排序
        addTimeList.sort(Comparator.comparing(ExpoAppointmentTime::getStartTime));
        if (addTimeList.get(0).getStartTime().isBefore(expoInfo.getStartTime()) || addTimeList.get(addTimeList.size() - 1).getEndTime().isAfter(expoInfo.getEndTime())) {
            throw new BusinessException(-1, "时间段超出展会时间范围");
        }

        // 保存预约信息
        ExpoAppointment appointment = CopyObjectUtils.copyAtoB(dto, ExpoAppointment.class);
        appointment.setStatus(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode());
        expoAppointmentManager.save(appointment);
        // 保存预约时间段
        addTimeList.forEach(v -> v.setAppointmentId(appointment.getId()));
        expoAppointmentTimeManager.saveBatch(addTimeList);
        // 添加附件
        expoAttachmentFileManager.addOrUpdateAttachmentFile(dto.getExpoId(), appointment.getId(), dto.getAttachmentList(), ExpoAttachmentFileEnum.Type.EXPO_APPOINT);
    }

    /**
     * 查询预约人员的预约时间段
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnelTimeVO>
     */
    @Override
    public List<ExpoAppointedPersonnelTimeVO> personnelTime(ExpoAppointedPersonnelTimeDTO dto) {
        ExpoAppointedPersonnel personnel = expoAppointedPersonnelManager.getById(dto.getPersonnelId());
        if (Objects.isNull(personnel)) {
            throw new BusinessException(-1, "预约人员不存在");
        }
        // 查询已设置的时间段（包含是否被预约标识）
        List<ExpoAppointedPersonnelTimeExt> timeList = expoAppointedPersonnelTimeManager.queryBy(dto.getPersonnelId());
        if (!CollectionUtils.isEmpty(timeList)) {
            return CopyObjectUtils.copyAlistToBlist(timeList, ExpoAppointedPersonnelTimeVO.class);
        }
        return null;
    }

    /**
     * 待处理预约列表
     *
     * @param dto 参数
     * @return List<AppointPendingListVO>
     */
    @Override
    public PageVO<AppointPendingListVO> pendingList(ExpoAppointmentDTO dto) {
        PageVO<AppointPendingListVO> pageVO = new PageVO<>();
        // 查询预约我的待确定的
        dto.setStatus(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode());
        List<ExpoAppointment> list = expoAppointmentManager.queryAppointMyList(dto);
        if (CollectionUtils.isEmpty(list)) {
            pageVO.setTotal(NumberConstant.ZERO);
            return pageVO;
        }
        pageVO.setTotal(list.size());
        List<AppointPendingListVO> pendingListVOS = CopyObjectUtils.copyAlistToBlist(list, AppointPendingListVO.class);
        // 取前3条
        pendingListVOS = pendingListVOS.subList(0, Math.min(list.size(), 3));
        // 查询预约时间段
        List<ExpoAppointmentTime> timeList = expoAppointmentTimeManager.queryByAppointmentId(pendingListVOS.stream().map(AppointPendingListVO::getId).collect(Collectors.toList()));
        Map<Integer, List<ExpoAppointmentTime>> timeMap = timeList.stream().collect(Collectors.groupingBy(ExpoAppointmentTime::getAppointmentId));
        // 公司信息
        Map<Integer, QueryCompanyResp> companyMap = new HashMap<>();
        // 成员信息
        Map<Integer, BatchMemberInfoResp> memberMap = new HashMap<>();
        // 观众信息
        Map<Integer, ExpoAudience> audienceMap = new HashMap<>();
        // 预约方业务类型
        Integer appointBusinessType = dto.getBusinessType();
        if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
            appointBusinessType = ExpoBusinessTypeEnum.EXHIBITOR.getCode();
            // 查询成员信息
            List<Integer> memberIds = pendingListVOS.stream().map(AppointPendingListVO::getMemberId).distinct().collect(Collectors.toList());
            memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
            // 查询公司信息
            List<Integer> companyIds = pendingListVOS.stream().map(AppointPendingListVO::getCompanyId).distinct().collect(Collectors.toList());
            companyMap = feignCommonManager.queryCompanyByIds(companyIds);
        } else if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            appointBusinessType = ExpoBusinessTypeEnum.AUDIENCE.getCode();
            // 查询观众信息
            List<Integer> audienceIds = pendingListVOS.stream().map(AppointPendingListVO::getBusinessId).distinct().collect(Collectors.toList());
            List<ExpoAudience> expoAudiences = expoAudienceManager.queryByIds(audienceIds);
            if (!CollectionUtils.isEmpty(expoAudiences)) {
                audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getId, v -> v));
            }
        }
        // 组装数据
        for (AppointPendingListVO pendingListVO : pendingListVOS) {
            if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                pendingListVO.setMemberName(audienceMap.getOrDefault(pendingListVO.getBusinessId(), new ExpoAudience()).getName());
                pendingListVO.setCompanyName(audienceMap.getOrDefault(pendingListVO.getBusinessId(), new ExpoAudience()).getCompanyName());
            }
            if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                pendingListVO.setMemberName(memberMap.getOrDefault(pendingListVO.getMemberId(), new BatchMemberInfoResp()).getName());
                pendingListVO.setCompanyName(companyMap.getOrDefault(pendingListVO.getCompanyId(), new QueryCompanyResp()).getCompanyName());
            }
            // 设置预约目的名称
            ExpoAppointPurposeEnum purposeEnum = ExpoAppointPurposeEnum.getByTypeAndCode(appointBusinessType, pendingListVO.getPurposeType());
            if (Objects.nonNull(purposeEnum)) {
                pendingListVO.setPurposeName(purposeEnum.getName());
            }
            // 设置预约时间
            pendingListVO.setTimeList(CopyObjectUtils.copyAlistToBlist(timeMap.getOrDefault(pendingListVO.getId(), new ArrayList<>()), ExpoAppointmentTimeVO.class));
        }
        pageVO.setList(pendingListVOS);
        return pageVO;
    }

    /**
     * 展会预约时间列表
     *
     * @param dto 参数
     * @return List<AppointTimeListVO>
     */
    @Override
    public List<AppointTimeListVO> timeList(ExpoAppointmentDTO dto) {
        List<ExpoAppointmentExt> list = expoAppointmentManager.queryAppointTimeList(dto);
        if (!CollectionUtils.isEmpty(list)) {
            List<AppointTimeListVO> appointTimeListVOS = CopyObjectUtils.copyAlistToBlist(list, AppointTimeListVO.class);
            // 成员信息
            Map<Integer, BatchMemberInfoResp> memberMap = new HashMap<>();
            // 观众信息
            Map<Integer, ExpoAudience> audienceMap = new HashMap<>();
            // 查询
            if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                List<Integer> memberIds = appointTimeListVOS.stream().map(AppointTimeListVO::getMemberId).distinct().collect(Collectors.toList());
                memberMap = feignCommonManager.queryMemberDetailsByIds(memberIds);
            } else if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                List<Integer> audienceIds = appointTimeListVOS.stream().map(AppointTimeListVO::getBusinessId).distinct().collect(Collectors.toList());
                List<ExpoAudience> expoAudiences = expoAudienceManager.queryByIds(audienceIds);
                if (!CollectionUtils.isEmpty(expoAudiences)) {
                    audienceMap = expoAudiences.stream().collect(Collectors.toMap(ExpoAudience::getId, v -> v));
                }
            }
            for (AppointTimeListVO appointTimeListVO : appointTimeListVOS) {
                if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                    appointTimeListVO.setMemberName(audienceMap.getOrDefault(appointTimeListVO.getBusinessId(), new ExpoAudience()).getName());
                }
                if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                    appointTimeListVO.setMemberName(memberMap.getOrDefault(appointTimeListVO.getMemberId(), new BatchMemberInfoResp()).getName());
                }
                // 状态名称
                appointTimeListVO.setStatusName(ExpoAppointmentStatusEnum.getNameByCode(appointTimeListVO.getStatus()));
            }
            return appointTimeListVOS;
        }
        return null;
    }

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return AppointDetailVO
     */
    @Override
    public AppointDetailVO appointDetail(ExpoAppointmentDTO dto) {
        ExpoAppointmentExt expoAppointmentExt = expoAppointmentManager.queryDetail(dto);
        if (Objects.isNull(expoAppointmentExt)) {
            throw new BusinessException(-1, "预约不存在");
        }
        AppointDetailVO appointDetailVO = CopyObjectUtils.copyAtoB(expoAppointmentExt, AppointDetailVO.class);
        // 状态
        appointDetailVO.setStatusName(ExpoAppointmentStatusEnum.getNameByCode(expoAppointmentExt.getStatus()));
        // 目的
        ExpoAppointPurposeEnum purposeEnum = ExpoAppointPurposeEnum.getByTypeAndCode(appointDetailVO.getBusinessType(), appointDetailVO.getPurposeType());
        if (Objects.nonNull(purposeEnum)) {
            appointDetailVO.setPurposeName(purposeEnum.getName());
        }
        // 时间段
        List<ExpoAppointmentTime> expoAppointmentTimes = expoAppointmentTimeManager.queryByAppointmentId(Collections.singletonList(dto.getId()));
        appointDetailVO.setTimeList(CopyObjectUtils.copyAlistToBlist(expoAppointmentTimes, ExpoAppointmentTimeVO.class));
        // 展位
        List<ExpoExhibitorBoothDTO> boothDTOS = expoExhibitorBoothManager.getDetailByExhibitorId(expoAppointmentExt.getExhibitorId());
        if (!CollectionUtils.isEmpty(boothDTOS)) {
            String boothNumberStr = boothDTOS.stream().map(ExpoExhibitorBoothDTO::getBoothNumber).collect(Collectors.joining(","));
            appointDetailVO.setBoothNumberStr(boothNumberStr);
        }
        // 附件
        List<ExpoAttachmentFileVO> attachmentFileList = expoAttachmentFileManager.getExpoAttachmentFileList(expoAppointmentExt.getExpoId(), dto.getId(), ExpoAttachmentFileEnum.Type.EXPO_APPOINT);
        appointDetailVO.setAttachmentList(attachmentFileList);
        // 设置成员与公司
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            ExpoAudience audience = expoAudienceManager.getOneById(expoAppointmentExt.getAudienceId());
            if (Objects.nonNull(audience)) {
                appointDetailVO.setMemberName(audience.getName());
                appointDetailVO.setCompanyName(audience.getCompanyName());
            }
        }
        if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
            // 成员
            MemberResp memberResp = feignCommonManager.getByMemberId(expoAppointmentExt.getMemberId());
            if (Objects.nonNull(memberResp)) {
                appointDetailVO.setMemberName(memberResp.getName());
            }
            // 公司
            QueryCompanyResp companyResp = feignCommonManager.selectCompanyById(expoAppointmentExt.getCompanyId());
            if (Objects.nonNull(companyResp)) {
                appointDetailVO.setCompanyName(companyResp.getCompanyName());
            }
        }
        return appointDetailVO;
    }

    /**
     * 处理预约
     *
     * @param dto
     */
    @Override
    public void appointHandle(ExpoAppointmentDTO dto) {
        ExpoAppointmentExt expoAppointment = expoAppointmentManager.queryDetail(dto);
        if (Objects.isNull(expoAppointment)) {
            throw new BusinessException(-1, "预约不存在");
        }
        // 需要更新的状态
        Integer newStatus = dto.getStatus();
        // 原状态
        Integer oldStatus = expoAppointment.getStatus();
        // 我预约的校验
        if (expoAppointment.getAppointType() == 1) {
            if (ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(newStatus) || ExpoAppointmentStatusEnum.REJECTED.getCode().equals(newStatus)) {
                throw new BusinessException(-1, "不能处理自己预约的");
            }
            if (ExpoAppointmentStatusEnum.CANCELLED.getCode().equals(newStatus)) {
                if (!(ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(oldStatus) || ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode().equals(oldStatus))) {
                    throw new BusinessException(-1, "待确认或已接受的预约才能取消");
                }
            }
        }
        // 预约我的校验
        if (expoAppointment.getAppointType() == 2) {
            if (ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(newStatus) || ExpoAppointmentStatusEnum.REJECTED.getCode().equals(newStatus)) {
                if (!oldStatus.equals(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode())) {
                    throw new BusinessException(-1, "待确认的预约才能操作");
                }
            }
            if (ExpoAppointmentStatusEnum.CANCELLED.getCode().equals(newStatus)) {
                if (!ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(oldStatus)) {
                    throw new BusinessException(-1, "待确认的预约才能取消");
                }
            }
        }
        // 更新状态
        ExpoAppointment update = new ExpoAppointment();
        update.setId(dto.getId());
        update.setStatus(newStatus);
        expoAppointmentManager.updateById(update);
        // todo 发送邮件和im通知

    }
}
