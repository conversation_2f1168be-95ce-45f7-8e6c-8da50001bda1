package com.echronos.expo.service;

import com.echronos.commons.Result;
import com.echronos.expo.dto.ExpoFormDTO;
import com.echronos.expo.dto.ExpoFormPageDTO;
import com.echronos.expo.dto.ExpoFormScanDTO;
import com.echronos.expo.model.ExpoForm;
import com.echronos.expo.vo.ExpoFormVO;
import com.echronos.expo.dto.*;
import com.echronos.expo.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/14 16:11
 * @ClassName IExpoService
 */
public interface IExpoFormService {


    /**
     * 查询展会表单列表
     * @param expoId 展会ID
     * @return
     */
    List<ExpoFormVO> getList(Integer expoId);

    /**
     * 创建/编辑表单
     *
     * @param dto
     */
    Result edit(ExpoFormDTO dto);

    /**
     * 启用/禁用表单
     * @param dto
     */
    void editIsEnable(ExpoFormDTO dto);


    /**
     * 分页查询表单
     *
     * @param dto
     * @return
     */
    Result<List<ExpoFormVO>> pageFor(ExpoFormPageDTO dto);

    /**
     * 查询表单列表
     *
     * @param dto
     * @return
     */
    Result<List<ExpoFormVO>> list(ExpoFormPageDTO dto);

    /**
     * 表单详情
     *
     * @param dto
     * @return
     */
    Result<ExpoFormVO> info(ExpoFormDTO dto);

    /**
     * 查询展会字段配置
     *
     * @param dto
     * @return
     */
    Result<ExpoFormVO> scanConfig(ExpoFormDTO dto);

    /**
     * 配置显示字段信息
     *
     * @param dto
     */
    Result editConfigScan(ExpoFormScanDTO dto);

    /**
     * 创建/编辑表单
     *
     * @param dto
     */
    Result del(ExpoFormDTO dto);

    /**
     * 复制表单
     * @param dto
     * @return
     */
    Result copyExpoForm(ExpoFormDTO dto);

    void addPrintConfig(ExpoPrintConfigDTO dto);

    Result<ExpoPrintConfigVO> queryPrintConfig(ExpoPrintConfigDTO dto);

    Result<ExpoFormFieldVO> queryFormConfig(ExpoFormFieldDTO dto);

    Result saveConfigScan(ExpoFormFieldDTO dto);

    Result<ExpoPrintConfigFieldVO> selectPrintConfig(ExpoPrintConfigDTO dto);

    Result<ExpoFormChoseFieldVO> getFormConfig(ExpoFormFieldDTO dto);

    /**
     * 根据类型获取表单
     * @param expoId 展会ID
     * @return
     */
    ExpoForm getFormByType(Integer expoId, Integer type);

}
