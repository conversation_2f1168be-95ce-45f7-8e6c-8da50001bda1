package com.echronos.expo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.service.IExpoFormService;
import com.echronos.expo.util.FormFieldsUtil;
import com.echronos.expo.vo.*;
import com.echronos.iform.api.resp.ComponentResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单管理
 *
 * <AUTHOR>
 * @Date 2025/5/14 17:13
 * @ClassName ExpoFormService
 */
@Slf4j
@Service
public class ExpoFormService implements IExpoFormService {
    @Resource
    private ExpoFormManager expoFormManager;

    @Resource
    private ExpoInfoManager expoInfoManager;

    @Resource
    private ExpoFormFieldManager formFieldManager;

    @Resource
    private ExpoChannelConfigManager expoChannelConfigManager;

    @Resource
    private FeignCommonManager feignCommonManager;
    @Resource
    private ExpoPrintConfigManager expoPrintConfigManager;
    @Resource
    private ExpoFormFieldManager expoFormFieldManager;


    @Override
    public List<ExpoFormVO> getList(Integer expoId) {
        List<ExpoForm> list = expoFormManager.getByExpoId(expoId);
        List<ExpoFormVO> listVO = null;
        if(CollectionUtils.isNotEmpty(list)){
            listVO = CopyObjectUtils.copyAlistToBlist(list, ExpoFormVO.class);
        }
        return listVO;
    }

    @Override
    public Result edit(ExpoFormDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
                .eq(ExpoInfo::getId, dto.getExpoId())
        );
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getMessage());
        }

        // 校验表单分组及表单类型
        ExpoFormEnums.FormType formTypeEnum = ExpoFormEnums.FormType.getByCode(dto.getFormType());
        if(null == formTypeEnum){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FROM_TYPE_ERROR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FROM_TYPE_ERROR.getMessage());
        } else if(formTypeEnum.getGroupId() != dto.getFormGroup()) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FROM_GROUP_TYPE_ERROR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FROM_GROUP_TYPE_ERROR.getMessage());
        }

        List<ExpoForm> forms = expoFormManager.list(new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
                .eq(ExpoForm::getExpoId, expoInfo.getId())
                .eq(ExpoForm::getFormGroup, dto.getFormGroup())
                .eq(ExpoForm::getFormType, dto.getFormType())
        );
        Set<Integer> ids = forms.stream().map(ExpoForm::getId).collect(Collectors.toSet());
        if (Objects.nonNull(dto.getId()) && ids.contains(dto.getId())) {
            ExpoForm expoForm = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                    .eq(ExpoForm::getId, dto.getId())
                    .eq(ExpoForm::getExpoId, expoInfo.getId())
                    .eq(ExpoForm::getCompanyId, dto.getCompanyId())
            );
            if (Objects.isNull(expoForm)) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
            }
        } else {
            // 非其它表单类型只能创建一个
            if(ExpoFormEnums.FormType.EXHIBITOR_OTHER.getCode() != formTypeEnum.getCode()){
                if (CollectionUtils.isNotEmpty(forms)) {
                    throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_CAN_NOT_CREATE.getCode(),
                            ExpoResultCode.ExpoResultEnum.EXPO_FORM_CAN_NOT_CREATE.getMessage());
                }
            }
            List<FormFieldDTO> audienceField = FormFieldsUtil.getAudienceField();
            dto.setFieldJson(JSONObject.toJSONString(audienceField));
            dto.setIsEnable(CommonStatus.StatusEnum.ENABLE.getValue());
        }
        expoFormManager.saveOrUpdate(dto);
        return Result.build();
    }

    @Override
    public void editIsEnable(ExpoFormDTO dto) {
        expoFormManager.updateIsEnable(dto.getId(), dto.getIsEnable(), dto.getUserId());
    }

    @Override
    public Result<List<ExpoFormVO>> pageFor(ExpoFormPageDTO dto) {
        Page<ExpoForm> formPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        Page<ExpoForm> page = expoFormManager.page(formPage, new LambdaQueryWrapper<ExpoForm>()
                .like(StringUtils.isNotBlank(dto.getKeywords()), ExpoForm::getFormName, dto.getKeywords())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
                .eq(null != dto.getFormGroup(), ExpoForm::getFormGroup, dto.getFormGroup())
                .eq(null != dto.getFormType(), ExpoForm::getFormType, dto.getFormType())
                .eq(ExpoForm::getExpoId, dto.getExpoId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoForm> records = page.getRecords();
        List<ExpoFormVO> vos = CopyObjectUtils.copyAlistToBlist(records, ExpoFormVO.class);
        return Result.build(vos, page.getTotal());
    }

    @Override
    public Result<List<ExpoFormVO>> list(ExpoFormPageDTO dto) {
        List<ExpoForm> forms = expoFormManager.list(new LambdaQueryWrapper<ExpoForm>()
                .like(StringUtils.isNotBlank(dto.getKeywords()), ExpoForm::getFormName, dto.getKeywords())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
                .eq(null != dto.getFormGroup(), ExpoForm::getFormType, dto.getFormType())
                .eq(ExpoForm::getExpoId, dto.getExpoId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoFormVO> vos = CopyObjectUtils.copyAlistToBlist(forms, ExpoFormVO.class);
        return Result.build(vos);
    }

    @Override
    public Result<ExpoFormVO> info(ExpoFormDTO dto) {
        ExpoForm expoForm = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                .eq(Objects.nonNull(dto.getExpoId()), ExpoForm::getExpoId, dto.getExpoId())
                .eq(Objects.nonNull(dto.getId()), ExpoForm::getId, dto.getId())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
        );
        ExpoFormVO vo = CopyObjectUtils.copyAtoB(expoForm, ExpoFormVO.class);
        return Result.build(vo);
    }

    @Override
    public Result<ExpoFormVO> scanConfig(ExpoFormDTO dto) {
        ExpoForm expoForm = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                .eq(Objects.nonNull(dto.getExpoId()), ExpoForm::getExpoId, dto.getExpoId())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
                .last("limit 1")
        );
        ExpoFormVO vo = CopyObjectUtils.copyAtoB(expoForm, ExpoFormVO.class);
        List<ExpoFormField> fieldList = formFieldManager.list(new LambdaQueryWrapper<ExpoFormField>()
                .eq(ExpoFormField::getExpoId, dto.getExpoId())
                .eq(ExpoFormField::getFormId, expoForm.getId())
        );
        if (Objects.nonNull(vo)) {
            vo.setFormFieldList(JSONObject.parseArray(vo.getFieldJson(), FormFieldVO.class));
        }
        vo.setFieldList(fieldList.stream().map(ExpoFormField::getColName).collect(Collectors.toList()));
        return Result.build(vo);
    }

    @Override
    public Result editConfigScan(ExpoFormScanDTO dto) {
        ExpoForm expoForm = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getId, dto.getId())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
        );
        if (Objects.isNull(expoForm)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        formFieldManager.remove(new LambdaQueryWrapper<ExpoFormField>()
                .eq(ExpoFormField::getFormId, expoForm.getId())
                .eq(ExpoFormField::getCompanyId, dto.getCompanyId())
        );
        ArrayList<ExpoFormField> expoFormFields = Lists.newArrayList();
        dto.getFieldList().stream().forEach(r -> {
            ExpoFormField field = CopyObjectUtils.copyAtoB(expoForm, ExpoFormField.class);
            field.setFormId(expoForm.getId());
            field.setColName(r);
            field.setId(null);
            expoFormFields.add(field);
        });
        formFieldManager.saveOrUpdateBatch(expoFormFields);
        return Result.build();
    }

    @Override
    public Result del(ExpoFormDTO dto) {

        List<ExpoChannelConfig> channelConfigs = expoChannelConfigManager.list(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getCompanyId, dto.getCompanyId())
                .eq(ExpoChannelConfig::getFormId, dto.getId())
        );
        if (CollectionUtils.isNotEmpty(channelConfigs)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_CAN_NOT_DEL.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_CAN_NOT_DEL.getMessage());
        }
        expoFormManager.remove(new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getId, dto.getId())
                .eq(ExpoForm::getCompanyId, dto.getCompanyId())
        );
        return Result.build();
    }

    @Override
    public Result copyExpoForm(ExpoFormDTO dto) {
        List<ExpoForm> expoFormList = expoFormManager.getByExpoId(dto.getExpoId());
        List<ExpoForm> copyFormList = expoFormManager.getByIds(dto.getFormIds(), CommonStatus.StatusEnum.ENABLE.getValue());
        if(copyFormList.size() != dto.getFormIds().size()){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        // 校验是否存在此类型表单
        if(CollectionUtils.isNotEmpty(expoFormList)){
            Map<String, List<ExpoForm>> expoFormMap = expoFormList.stream().collect(Collectors.groupingBy(form -> form.getFormGroup() + "_" + form.getFormType()));
            for(ExpoForm form : copyFormList){
                if(ExpoFormEnums.FormType.EXHIBITOR_OTHER.getCode() != form.getFormType()){
                    // 业务表单类型已存在
                    if(expoFormMap.containsKey(form.getFormGroup() + "_" + form.getFormType())){
                        throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_TYPE_EXIST.getCode(),
                                String.format(ExpoResultCode.ExpoResultEnum.EXPO_FORM_TYPE_EXIST.getMessage(),
                                        ExpoFormEnums.FormType.getByCode(form.getFormType()).getMsg()));
                    }
                }
            }
        }

        List<String> copyFormCodeList = copyFormList.stream().map(e -> e.getFormCode()).collect(Collectors.toList());
        Map<String, String> copyCodeNewCodeMap = feignCommonManager.copyForm(dto.getCompanyId(), copyFormCodeList, dto.getUserId());
        List<ExpoForm> addList = new ArrayList<>();
        copyFormList.stream().forEach(r -> {
            String newFormCode = copyCodeNewCodeMap.get(r.getFormCode());
            ExpoForm addExpoForm = CopyObjectUtils.copyAtoB(r, ExpoForm.class);
            addExpoForm.setId(null);
            addExpoForm.setVersionNumber(null);
            addExpoForm.setIsEnable(CommonStatus.StatusEnum.ENABLE.getValue());
            addExpoForm.setFormCode(newFormCode);
            addExpoForm.setExpoId(dto.getExpoId());
            addExpoForm.setCreateTime(LocalDateTime.now());
            addExpoForm.setCreateUser(dto.getUserId());
            addList.add(addExpoForm);
        });
        expoFormManager.saveBatch(addList);
        return new Result();
    }

    @Override
    public ExpoForm getFormByType(Integer expoId, Integer type) {
        ExpoForm expoForm = expoFormManager.getFormByFormType(expoId, type, null);
        if(null == expoForm){
            // 表单未配置
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_CONFIGURED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_CONFIGURED.getMessage());
        }else if(expoForm.getIsEnable() != CommonStatus.YesOrNoEnum.YES.getValue()){
            // 表单未启用
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_ENABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_ENABLED.getMessage());
        }
        return expoForm;
    }


    /**
     * 保存打印配置
     *
     * @param dto
     */
    @Override
    public void addPrintConfig(ExpoPrintConfigDTO dto) {
        ExpoPrintConfig expoPrintConfig = expoPrintConfigManager.queryOne(dto);
        ExpoPrintConfig printConfig;
        if (Objects.nonNull(expoPrintConfig)) {
            printConfig = CopyObjectUtils.copyAtoB(dto, ExpoPrintConfig.class);
            printConfig.setId(expoPrintConfig.getId());
        } else {
            printConfig = CopyObjectUtils.copyAtoB(dto, ExpoPrintConfig.class);
        }
        printConfig.setFieldJson(JSONObject.toJSONString(dto.getFieldList()));
        printConfig.setFormId(dto.getFormId());
        expoPrintConfigManager.saveOrUpdate(printConfig);
    }

    /**
     * 查询打印配置（后台）
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ExpoPrintConfigVO> queryPrintConfig(ExpoPrintConfigDTO dto) {
        ExpoForm expoForm = expoFormManager.getForm(dto.getExpoId(), 1, 1);
        if (Objects.isNull(expoForm)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(), ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        ExpoPrintConfigVO vo = new ExpoPrintConfigVO();
        List<ComponentResp> allComponent = new ArrayList<>();
        // 通过编码获取表字段
        List<ComponentResp> components = feignCommonManager.getComponents(expoForm.getFormCode(), dto.getCompanyId(), null);
        if (!CollectionUtils.isEmpty(components)) {
            // 收集所有有效字段
            allComponent = components.stream().filter(component -> {
                Map<String, Object> attr = component.getAttr();
                return attr.containsKey("label") && attr.containsKey("required");
            }).collect(Collectors.toList());
        }
        // 获取已选字段
        ExpoPrintConfig expoPrintConfig = expoPrintConfigManager.queryOne(dto);
        List<String> selectedFields;
        if (Objects.nonNull(expoPrintConfig)) {
            selectedFields = JSONObject.parseArray(expoPrintConfig.getFieldJson(), String.class);
            vo.setIsContainQr(expoPrintConfig.getIsContainQr());
        } else {
            selectedFields = new ArrayList<>();
        }
        // 构建字段状态列表
        List<FieldStatusVO> fieldStatusList;
        if (CollectionUtils.isEmpty(allComponent)) {
            fieldStatusList = new ArrayList<>();
        } else {
            fieldStatusList = allComponent.stream().map(component -> {
                Map<String, Object> attr = component.getAttr();
                String label = (String) attr.get("label");
                Boolean required = (Boolean) attr.get("required");
                // 判断字段是否已选 (0-已选，1-未选)
                boolean isSelected = !CollectionUtils.isEmpty(selectedFields) && selectedFields.contains(label);
                return new FieldStatusVO(label, isSelected ? 0 : 1, component.getIsSystem(), component.getType(), required);
            }).collect(Collectors.toList());
        }
        vo.setFieldList(fieldStatusList);
        return Result.build(vo);
    }

    /**
     * 保存扫码配置
     *
     * @param dto
     */
    @Override
    public Result saveConfigScan(ExpoFormFieldDTO dto) {
        ExpoForm expoForm = expoFormManager.getForm(Math.toIntExact(dto.getExpoId()), 1, 1);
        if (Objects.isNull(expoForm)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(), ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        ExpoFormField expoFormField = expoFormFieldManager.queryOne(dto);
        ArrayList<ExpoFormField> expoFormFields = new ArrayList<>();
        dto.getFieldList().forEach(fieldName -> {
            ExpoFormField field;
            // 如果已存在配置，则更新；否则新增
            if (Objects.nonNull(expoFormField)) {
                field = CopyObjectUtils.copyAtoB(expoForm, ExpoFormField.class);
                field.setId(expoFormField.getId());
            } else {
                field = CopyObjectUtils.copyAtoB(expoForm, ExpoFormField.class);
            }
            field.setFormId(dto.getFormId());
            field.setColName(fieldName);
            expoFormFields.add(field);
        });
//        // 查询表单ID
//        ExpoForm expoForm = expoFormManager.getForm(Math.toIntExact(dto.getExpoId()), 1, 1);
//        if (Objects.isNull(expoForm)) {
//            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(), ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
//        }
//        ArrayList<ExpoFormField> expoFormFields = new ArrayList<>();
//        dto.getFieldList().stream().forEach(r -> {
//            ExpoFormField field = CopyObjectUtils.copyAtoB(expoForm, ExpoFormField.class);
//            field.setFormId(dto.getId());
//            field.setColName(r);
//            expoFormFields.add(field);
//        });
        formFieldManager.saveOrUpdateBatch(expoFormFields);
        return Result.build();
    }

    /**
     * 获取打印配置（前台）
     *
     * @param dto
     */
    @Override
    public Result<ExpoPrintConfigFieldVO> selectPrintConfig(ExpoPrintConfigDTO dto) {
        ExpoPrintConfig expoPrintConfig = expoPrintConfigManager.queryOne(dto);
        ExpoPrintConfigFieldVO vo = new ExpoPrintConfigFieldVO();
        if (Objects.nonNull(expoPrintConfig)) {
            vo.setFieldList(JSONObject.parseArray(expoPrintConfig.getFieldJson(), String.class));
            vo.setIsContainQr(expoPrintConfig.getIsContainQr());
            vo.setId(expoPrintConfig.getId());
        }
        return Result.build(vo);
    }

    /**
     * 获取扫码配置（前台）
     *
     * @param dto
     */
    @Override
    public Result<ExpoFormChoseFieldVO> getFormConfig(ExpoFormFieldDTO dto) {
        List<ExpoFormField> expoFormFields = expoFormFieldManager.queryList(dto);
        ExpoFormChoseFieldVO vo = new ExpoFormChoseFieldVO();
        if (!CollectionUtils.isEmpty(expoFormFields)) {
            vo.setFieldList(expoFormFields.stream().map(ExpoFormField::getColName).collect(Collectors.toList()));
            vo.setId(expoFormFields.get(0).getId());
        }
        return Result.build(vo);
    }

    /**
     * 查询扫码配置
     *
     * @param dto
     */
    @Override
    public Result<ExpoFormFieldVO> queryFormConfig(ExpoFormFieldDTO dto) {
        ExpoForm expoForm = expoFormManager.getForm(dto.getExpoId(), 1, 1);
        if (Objects.isNull(expoForm)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(), ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        ExpoFormFieldVO vo = new ExpoFormFieldVO();
        List<ComponentResp> allComponent = new ArrayList<>();
        // 通过编码获取表字段
        List<ComponentResp> components = feignCommonManager.getComponents(expoForm.getFormCode(), dto.getCompanyId(), null);
        if (!CollectionUtils.isEmpty(components)) {
            allComponent = components.stream().filter(component -> {
                Map<String, Object> attr = component.getAttr();
                return attr.containsKey("label") && attr.containsKey("required");
            }).collect(Collectors.toList());
        }
        // 获取已选字段
        List<ExpoFormField> expoFormFields = expoFormFieldManager.queryList(dto);
        List<String> selectedFields;
        if (!CollectionUtils.isEmpty(expoFormFields)) {
            selectedFields = expoFormFields.stream().map(ExpoFormField::getColName).collect(Collectors.toList());
        } else {
            selectedFields = new ArrayList<>();
        }
        // 构建字段状态列表
        List<FieldStatusVO> fieldStatusList;
        if (CollectionUtils.isEmpty(allComponent)) {
            fieldStatusList = new ArrayList<>();
        } else {
            fieldStatusList = allComponent.stream().map(component -> {
                Map<String, Object> attr = component.getAttr();
                String label = (String) attr.get("label");
                Boolean required = (Boolean) attr.get("required");
                // 判断字段是否已选 (0-已选，1-未选)
                boolean isSelected = !CollectionUtils.isEmpty(selectedFields) && selectedFields.contains(label);
                return new FieldStatusVO(label, isSelected ? 0 : 1, component.getIsSystem(), component.getType(), required);
            }).collect(Collectors.toList());
        }
        vo.setFieldList(fieldStatusList);
        return Result.build(vo);
    }
}
