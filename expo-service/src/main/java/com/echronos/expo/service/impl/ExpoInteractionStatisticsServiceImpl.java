package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.page.PageVO;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.ExpoAppointPurposeEnum;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.model.ext.*;
import com.echronos.expo.service.IExpoInteractionStatisticsService;
import com.echronos.expo.util.DateUtil;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.appoint.ExpoAppointmentTimeVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailCountVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailListTimeVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailListVO;
import com.echronos.expo.vo.statistics.ExpoAppointDetailVO;
import com.echronos.user.api.resp.UserInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展会互动统计服务实现类
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@Service
public class ExpoInteractionStatisticsServiceImpl implements IExpoInteractionStatisticsService {

    @Resource
    private ExpoExhibitorScanRecordManager expoExhibitorScanRecordManager;
    @Resource
    private ExpoAppointmentManager expoAppointmentManager;
    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAppointmentTimeManager expoAppointmentTimeManager;
    @Resource
    private ExpoExhibitorBoothManager expoExhibitorBoothManager;
    @Resource
    private FeignCommonManager feignCommonManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;

    /**
     * 展会互动次数统计数据
     *
     * @param dto 统计参数
     * @return 互动次数统计结果
     */
    @Override
    public ExpoInteractionStatisticsVO interactionStatistics(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        // 展商扫码次数
        Integer exhibitorScanCount = expoExhibitorScanRecordManager.getExhibitorScanRecordsCount(dto.getExpoId());
        // 观众预约次数
        Integer appointmentCount = expoAppointmentManager.getAppointmentCount(dto.getExpoId());
        int totalInteractionCount = exhibitorScanCount + appointmentCount;
        ExpoInteractionStatisticsVO vo = new ExpoInteractionStatisticsVO();
        // 展商扫码转化率
        BigDecimal exhibitorScanConversionRate = totalInteractionCount == 0 ? BigDecimal.ZERO :
                new BigDecimal(exhibitorScanCount).divide(new BigDecimal(totalInteractionCount), NumberConstant.FOUR, RoundingMode.HALF_UP);
        vo.setExhibitorScanConversionRate(exhibitorScanConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        // 观众预约转化率
        BigDecimal audienceAppointmentConversionRate = totalInteractionCount == 0 ? BigDecimal.ZERO :
                new BigDecimal(appointmentCount).divide(new BigDecimal(totalInteractionCount), NumberConstant.FOUR, RoundingMode.HALF_UP);
        vo.setAudienceAppointmentConversionRate(audienceAppointmentConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        vo.setAudienceAppointmentCount(appointmentCount);
        vo.setTotalInteractionCount(totalInteractionCount);
        vo.setExhibitorScanCount(exhibitorScanCount);
        return vo;
    }

    /**
     * 预约状态分布统计
     *
     * @param dto 统计参数
     * @return
     */
    @Override
    public ExpoAppointStatusVO appointmentStatusStatistics(ExpoInteractionStatisticsDTO dto) {
        List<ExpoAppointment> expoAppointmentList = expoAppointmentManager.getAppointmentList(dto.getExpoId());
        if (CollectionUtil.isEmpty(expoAppointmentList)) {
            return null;
        }
        ExpoAppointStatusVO vo = new ExpoAppointStatusVO();
        //待确认的预约数据
        List<ExpoAppointment> toConfirmList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已接受的预约数据
        List<ExpoAppointment> acceptedList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已拒绝的预约数据
        List<ExpoAppointment> rejectedList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.REJECTED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已取消的预约数据
        List<ExpoAppointment> cancelledList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.CANCELLED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(toConfirmList)) {
            BigDecimal toConfirmRate = new BigDecimal(toConfirmList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToConfirmRate(toConfirmRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        }
        if (CollectionUtil.isNotEmpty(acceptedList)) {
            BigDecimal acceptedRate = new BigDecimal(acceptedList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setAcceptedRate(acceptedRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        }
        if (CollectionUtil.isNotEmpty(rejectedList)) {
            BigDecimal rejectedRate = new BigDecimal(rejectedList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setRejectedRate(rejectedRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        }
        if (CollectionUtil.isNotEmpty(cancelledList)) {
            BigDecimal cancelledRate = new BigDecimal(cancelledList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setCancelledRate(cancelledRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
        }
        return vo;
    }

    /**
     * 每日互动趋势统计
     *
     * @param dto 统计参数
     * @return 每日互动趋势数据
     */
    @Override
    public List<ExpoInteractionTrendVO> interactionTrendStatistics(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        // 获取展会期间的日期范围
        List<LocalDate> expoDates = getExpoDates(dto);
        // 获取展商扫码数据
        List<ExpoExhibitorScanCountExt> scanRecords = expoExhibitorScanRecordManager.getScanRecordsByDate(dto.getExpoId());
        Map<LocalDate, Integer> scanDataMap = new HashMap<>();
        for (ExpoExhibitorScanCountExt record : scanRecords) {
            LocalDate date = record.getScanDate();
            Integer count = record.getScanCount();
            scanDataMap.put(date, count);
        }
        // 获取预约数据
        List<ExpoAppointmentCountExt> appointmentRecords = expoAppointmentManager.getAppointmentsByDate(dto.getExpoId());
        Map<LocalDate, Integer> appointmentDataMap = new HashMap<>();
        for (ExpoAppointmentCountExt record : appointmentRecords) {
            LocalDate date = record.getAppointmentDate();
            Integer count = record.getAppointmentCount();
            appointmentDataMap.put(date, count);
        }
        List<ExpoInteractionTrendVO> trendVOS = new ArrayList<>();
        for (LocalDate date : expoDates) {
            ExpoInteractionTrendVO vo = new ExpoInteractionTrendVO();
            vo.setDate(date);
            vo.setExhibitorScanCount(scanDataMap.getOrDefault(date, 0));
            vo.setAppointmentCount(appointmentDataMap.getOrDefault(date, 0));
        }
        return trendVOS;
    }

    /**
     * 获取展会日期列表
     *
     * @param dto 统计参数
     * @return 展会日期列表
     */
    @Override
    public List<LocalDate> getExpoDates(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        LocalDate startDate = expoInfo.getStartTime().toLocalDate();
        LocalDate endDate = expoInfo.getEndTime().toLocalDate();
        // 生成日期列表
        List<LocalDate> dateList = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dateList;
    }

    /**
     * 每小时互动分布统计
     *
     * @param dto 统计参数
     * @return 每小时互动分布数据
     */
    @Override
    public List<ExpoHourlyInteractionVO> hourlyInteractionStatistics(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        // 获取展商扫码数据
        List<ExpoExhibitorScanCountExt> scanRecords = expoExhibitorScanRecordManager.getScanRecordsByHour(dto.getExpoId(), dto.getDate());
        Map<LocalTime, Integer> scanDataMap = new HashMap<>();
        for (ExpoExhibitorScanCountExt record : scanRecords) {
            LocalTime time = record.getScanTime();
            Integer count = record.getScanCount();
            scanDataMap.put(time, count);
        }
        // 获取预约数据
        List<ExpoAppointmentCountExt> appointmentRecords = expoAppointmentManager.getAppointmentsByHour(dto.getExpoId(), dto.getDate());
        Map<LocalTime, Integer> appointmentDataMap = new HashMap<>();
        for (ExpoAppointmentCountExt record : appointmentRecords) {
            LocalTime time = record.getAppointmentHour();
            Integer count = record.getAppointmentCount();
            appointmentDataMap.put(time, count);
        }
        List<ExpoHourlyInteractionVO> interactionVOS = new ArrayList<>();
        for (int hour = 9; hour <= 16; hour++) {
            ExpoHourlyInteractionVO vo = new ExpoHourlyInteractionVO();
            LocalTime time = LocalTime.of(hour, 0);
            int scanCount = scanDataMap.getOrDefault(time, 0);
            int appointmentCount = appointmentDataMap.getOrDefault(time, 0);
            vo.setHour(time);
            vo.setInteractionCount(scanCount + appointmentCount);
            interactionVOS.add(vo);
        }
        return interactionVOS;
    }

    /**
     * 互动展商排行统计
     *
     * @param dto 统计参数
     * @return 互动展商排行数据
     */
    @Override
    public ExpoPageVO<ExpoExhibitorInteractionRankingVO> exhibitorInteractionRankingStatistics(ExpoInteractionStatisticsDTO dto) {
        // 获取展商互动排行数据
        dto.setPageNo((dto.getPageNo() - 1) * dto.getPageSize());
        List<ExpoExhibitorRankingExt> rankingList = expoExhibitorManager.getExhibitorInteractionRanking(dto);
        // 获取互动展商排行总数
        Integer total = expoExhibitorManager.getExhibitorInteractionRankingCount(dto.getExpoId());
        List<ExpoExhibitorInteractionRankingVO> rankingVOS = CopyObjectUtils.copyAlistToBlist(rankingList, ExpoExhibitorInteractionRankingVO.class);
        return new ExpoPageVO<>(total, total, rankingVOS);
    }

    /**
     * 展商扫码详情总计
     *
     * @param dto 统计参数
     * @return 展商扫码详情总计
     */
    @Override
    public ExpoExhibitorScanCountVO exhibitorScanDetailTotal(ExpoInteractionStatisticsDTO dto) {
        ExpoExhibitorScanRecordDTO exhibitorScanRecordDTO = new ExpoExhibitorScanRecordDTO();
        exhibitorScanRecordDTO.setExpoId(dto.getExpoId());
        exhibitorScanRecordDTO.setExhibitorId(dto.getExhibitorId());
        List<ExpoExhibitorScanRecord> exhibitorScanRecords = expoExhibitorScanRecordManager.queryList(exhibitorScanRecordDTO);
        // 总扫码次数
        int totalScanCount = exhibitorScanRecords.size();
        // 参与展商数
        List<Integer> exhibitorIds = exhibitorScanRecords.stream().map(ExpoExhibitorScanRecord::getExhibitorId).distinct().collect(Collectors.toList());
        // 参与观众数
        List<Integer> audienceIds = exhibitorScanRecords.stream().map(ExpoExhibitorScanRecord::getAudienceId).distinct().collect(Collectors.toList());
        // 平均扫码数
        Integer avgScanCount = !exhibitorIds.isEmpty() ? totalScanCount / exhibitorIds.size() : 0;
        ExpoExhibitorScanCountVO exhibitorScanCountVO = new ExpoExhibitorScanCountVO();
        exhibitorScanCountVO.setTotalScanCount(totalScanCount);
        exhibitorScanCountVO.setExhibitorCount(exhibitorIds.size());
        exhibitorScanCountVO.setAudienceCount(audienceIds.size());
        exhibitorScanCountVO.setAverageScanCount(avgScanCount);
        return exhibitorScanCountVO;
    }

    /**
     * 展商扫码记录列表
     *
     * @param dto 统计参数
     * @return 展商扫码记录列表
     */
    @Override
    public ExpoPageVO<ExpoExhibitorScanListVO> exhibitorScanRecordList(ExpoInteractionStatisticsDTO dto) {
        Page<ExpoExhibitorScanRecordDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        ExpoExhibitorScanRecordDTO scanRecordDTO = new ExpoExhibitorScanRecordDTO();
        scanRecordDTO.setExpoId(dto.getExpoId());
        List<ExpoExhibitorScanRecordExt> scanRecordList = expoExhibitorScanRecordManager.pageExhibitorScanRecordList(page, scanRecordDTO);
        List<ExpoExhibitorScanListVO> scanListVOS = CopyObjectUtils.copyAlistToBlist(scanRecordList, ExpoExhibitorScanListVO.class);
        return new ExpoPageVO<>(page.getTotal(), page.getSize(), scanListVOS);
    }

    /**
     * 展商扫码观众详情列表
     *
     * @param dto 统计参数
     * @return 展商扫码观众详情列表
     */
    @Override
    public ExpoPageVO<ExpoExhibitorScanAudienceVO> exhibitorScanAudienceList(ExpoInteractionStatisticsDTO dto) {
        Page<ExpoExhibitorScanRecordDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        ExpoExhibitorScanRecordDTO scanRecordDTO = new ExpoExhibitorScanRecordDTO();
        scanRecordDTO.setExpoId(dto.getExpoId());
        scanRecordDTO.setExhibitorId(dto.getExhibitorId());
        List<ExpoExhibitorScanRecordDTO> scanAudienceList = expoExhibitorScanRecordManager.pageExhibitorScanAudienceList(page, scanRecordDTO);
        List<ExpoExhibitorScanAudienceVO> scanAudienceVOS = CopyObjectUtils.copyAlistToBlist(scanAudienceList, ExpoExhibitorScanAudienceVO.class);
        return new ExpoPageVO<>(page.getTotal(), page.getSize(), scanAudienceVOS);
    }

    /**
     * 预约详情预约数统计
     *
     * @param dto 参数
     * @return ExpoAppointDetailCountVO
     */
    @Override
    public ExpoAppointDetailCountVO appointDetailCount(ExpoAppointmentDTO dto) {
        ExpoAppointmentCountExt countExt = expoAppointmentManager.queryDetailCount(dto.getExpoId());
        return CopyObjectUtils.copyAtoB(countExt, ExpoAppointDetailCountVO.class);
    }

    /**
     * 预约详情预约列表
     *
     * @param dto 参数
     * @return List<ExpoAppointDetailListVO>
     */
    @Override
    public PageVO<ExpoAppointDetailListVO> appointDetailList(ExpoAppointmentDTO dto) {
        PageVO<ExpoAppointDetailListVO> pageVO = new PageVO<>();
        List<ExpoAppointmentExt> list = new ArrayList<>();
        if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
            list = expoAppointmentManager.queryAudienceAppointList(dto);
        }
        if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
            list = expoAppointmentManager.queryExhibitorAppointList(dto);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            // 预约ID
            List<Integer> appointIds = list.stream().map(ExpoAppointmentExt::getId).distinct().collect(Collectors.toList());
            // 查询预约的时间段
            List<ExpoAppointmentTime> times = expoAppointmentTimeManager.queryByAppointmentId(appointIds);
            Map<Integer, List<ExpoAppointmentTime>> timeMap = times.stream().collect(Collectors.groupingBy(ExpoAppointmentTime::getAppointmentId));
            // 展位
            Map<Integer, List<ExpoExhibitorBoothDTO>> boothMap = new HashMap<>();
            // 展商公司
            Map<Integer, QueryCompanyResp> companyMap = new HashMap<>();
            if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                // 查询展位信息
                List<Integer> exhibitorIds = list.stream().map(ExpoAppointment::getBusinessId).distinct().collect(Collectors.toList());
                boothMap = expoExhibitorBoothManager.getDetailByExhibitorIdList(exhibitorIds);
                // 查询展商头像
                List<Integer> companyIds = list.stream().map(ExpoAppointment::getCompanyId).distinct().collect(Collectors.toList());
                companyMap = feignCommonManager.queryCompanyByIds(companyIds);
            }
            // 观众用户
            Map<Integer, UserInfoResp> userMap = new HashMap<>();
            if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                List<Integer> userIds = list.stream().map(ExpoAppointment::getCreateUser).distinct().collect(Collectors.toList());
                userMap = feignCommonManager.batchUserById(userIds);
            }
            // 组装数据
            List<ExpoAppointDetailListVO> voList = new ArrayList<>();
            Map<Integer, List<ExpoAppointmentExt>> businessMap = list.stream().collect(Collectors.groupingBy(ExpoAppointmentExt::getBusinessId));
            Map<Integer, List<ExpoExhibitorBoothDTO>> finalBoothMap = boothMap;
            Map<Integer, UserInfoResp> finalUserMap = userMap;
            Map<Integer, QueryCompanyResp> finalCompanyMap = companyMap;
            businessMap.forEach((businessId, businessList) -> {
                ExpoAppointmentExt expoAppointmentExt = businessList.get(0);
                ExpoAppointDetailListVO vo = new ExpoAppointDetailListVO();
                vo.setName(expoAppointmentExt.getName());
                vo.setAudienceCompanyName(expoAppointmentExt.getAudienceCompanyName());
                // 设置展位
                if (finalBoothMap.containsKey(businessId)) {
                    String boothNumberStr = finalBoothMap.get(businessId).stream().map(ExpoExhibitorBoothDTO::getBoothNumber).collect(Collectors.joining(","));
                    vo.setExhibitorBoothNumber(boothNumberStr);
                }
                // 设置头像
                if (ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(dto.getBusinessType())) {
                    vo.setAvatar(finalUserMap.getOrDefault(expoAppointmentExt.getCreateUser(), new UserInfoResp()).getAvatar());
                }
                if (ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(dto.getBusinessType())) {
                    vo.setAvatar(finalCompanyMap.getOrDefault(expoAppointmentExt.getCompanyId(), new QueryCompanyResp()).getLogoUrl());
                }
                // 设置预约时间
                List<ExpoAppointDetailListTimeVO> timeList = new ArrayList<>();
                for (ExpoAppointmentExt appointmentExt : businessList) {
                    List<ExpoAppointmentTime> appointmentTimeList = timeMap.get(appointmentExt.getId());
                    for (ExpoAppointmentTime expoAppointmentTime : appointmentTimeList) {
                        ExpoAppointDetailListTimeVO timeVO = CopyObjectUtils.copyAtoB(expoAppointmentTime, ExpoAppointDetailListTimeVO.class);
                        timeVO.setStatus(appointmentExt.getStatus());
                        timeVO.setStatusName(ExpoAppointmentStatusEnum.getNameByCode(appointmentExt.getStatus()));
                        timeList.add(timeVO);
                    }
                }
                vo.setTimeList(timeList);
                voList.add(vo);
            });
            pageVO.setList(voList);
            pageVO.setTotal(list.size());
        }
        return pageVO;
    }

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return ExpoAppointDetailVO
     */
    @Override
    public ExpoAppointDetailVO appointDetail(ExpoAppointmentDTO dto) {
        ExpoAppointmentExt expoAppointmentExt = expoAppointmentManager.queryStatisticsDetail(dto.getId());
        if (Objects.nonNull(expoAppointmentExt)) {
            ExpoAppointDetailVO vo = CopyObjectUtils.copyAtoB(expoAppointmentExt, ExpoAppointDetailVO.class);
            // 状态
            vo.setStatusName(ExpoAppointmentStatusEnum.getNameByCode(expoAppointmentExt.getStatus()));
            // 目的
            ExpoAppointPurposeEnum purposeEnum = ExpoAppointPurposeEnum.getByTypeAndCode(expoAppointmentExt.getBusinessType(), expoAppointmentExt.getPurposeType());
            if (Objects.nonNull(purposeEnum)) {
                vo.setPurposeName(purposeEnum.getName());
            }
            // 时间段
            List<ExpoAppointmentTime> expoAppointmentTimes = expoAppointmentTimeManager.queryByAppointmentId(Collections.singletonList(dto.getId()));
            vo.setTimeList(CopyObjectUtils.copyAlistToBlist(expoAppointmentTimes, ExpoAppointmentTimeVO.class));
            // 展位
            List<ExpoExhibitorBoothDTO> boothDTOS = expoExhibitorBoothManager.getDetailByExhibitorId(expoAppointmentExt.getExhibitorId());
            if (!CollectionUtils.isEmpty(boothDTOS)) {
                String boothNumberStr = boothDTOS.stream().map(ExpoExhibitorBoothDTO::getBoothNumber).collect(Collectors.joining(","));
                vo.setBoothNumberStr(boothNumberStr);
            }
            // 查询展商公司信息
            QueryCompanyResp companyResp = feignCommonManager.selectCompanyById(expoAppointmentExt.getCompanyId());
            if (Objects.nonNull(companyResp)) {
                vo.setExhibitorName(companyResp.getCompanyName());
                vo.setExhibitorAvatar(companyResp.getLogoUrl());
            }
            // 查询观众
            ExpoAudience audience = expoAudienceManager.getOneById(expoAppointmentExt.getAudienceId());
            if (Objects.nonNull(audience)) {
                vo.setAudienceName(audience.getName());
            }
            // 查询观众头像
            UserInfoResp userInfoResp = feignCommonManager.getUserById(expoAppointmentExt.getCreateUser());
            if (Objects.nonNull(userInfoResp)) {
                vo.setAudienceAvatar(userInfoResp.getAvatar());
            }
            return vo;
        }
        return null;
    }

    /**
     * 互动分析报表统计
     *
     * @param dto 统计参数
     * @return 互动分析报表数据
     */
    @Override
    public ExpoInteractionReportVO report(ExpoInteractionStatisticsDTO dto) {
        // 获取到开始时间和结束时间
        DateTimeDTO dateTimeDto = DateUtil.getStartAndEndTime(dto.getDateScreeType());
        // 获取展商扫码次数
        Integer exhibitorScanCount = expoExhibitorScanRecordManager.getExhibitorScanRecordsCountTotal(dto.getCompanyId(),
                dateTimeDto.getStartTime(), dateTimeDto.getEndTime());
        // 获取观众预约次数
        Integer audienceCount = expoAppointmentManager.getAppointmentCountTotal(dto.getCompanyId(),
                dateTimeDto.getStartTime(), dateTimeDto.getEndTime());
        // todo 获取名片交换次数
        Integer cardExchangeCount = 0;
        // 总互动次数
        int totalInteractionCount = exhibitorScanCount + audienceCount + cardExchangeCount;
        BigDecimal scanRate = BigDecimal.valueOf(exhibitorScanCount).divide(BigDecimal.valueOf(totalInteractionCount), 2, RoundingMode.HALF_UP);
        BigDecimal appointmentRate = BigDecimal.valueOf(audienceCount).divide(BigDecimal.valueOf(totalInteractionCount), 2, RoundingMode.HALF_UP);
        BigDecimal cardExchangeRate = BigDecimal.valueOf(cardExchangeCount).divide(BigDecimal.valueOf(totalInteractionCount), 2, RoundingMode.HALF_UP);
        // 总互动次数较上月同期百分比

        ExpoInteractionReportVO vo = new ExpoInteractionReportVO();
        vo.setTotalInteractionCount(totalInteractionCount);
        vo.setScanCount(exhibitorScanCount);
        vo.setAppointmentCount(audienceCount);
        vo.setCardExchangeCount(cardExchangeCount);
        vo.setScanRate(scanRate);
        vo.setAppointmentRate(appointmentRate);
        vo.setCardExchangeRate(cardExchangeRate);
        return vo;
    }
}
