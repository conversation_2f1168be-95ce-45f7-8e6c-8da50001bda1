package com.echronos.expo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 观众导入DTO
 * <AUTHOR>
 * @date 2025-08-26 16:12
 */
@Data
public class AudienceExcelImportDTO {

    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 渠道
     */
    private String channelName;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司网站
     */
    private String companyWebsite;
    /**
     * 您想注册成为专业买家吗？
     */
    private String asBuyer;
    /**
     * 是否是买家：0否 1是
     */
    private Integer isBuyer;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 提交时间（文本）
     */
    private String createTimeText;
    /**
     * 提交时间
     */
    private LocalDateTime createTime;
    /**
     * 表单数据
     */
    Map<String,String> extMap;

}
