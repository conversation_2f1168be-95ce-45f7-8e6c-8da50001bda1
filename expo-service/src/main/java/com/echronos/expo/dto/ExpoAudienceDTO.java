/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoAudience;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * ExpoAudience  Dto 对象
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class ExpoAudienceDTO extends ExpoAudience {
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 是否发送邮件
     */
    private String isSend;
    /**
     * 邮件发送次数
     */
    private Integer sendCount;
    /**
     * 最近发送时间
     */
    private LocalDateTime lastSendTime;
    /**
     * 是否签到
     */
    private String isSign;
    /**
     * 最近签到
     */
    private LocalDateTime lastSignTime;
    /**
     * 签到次数
     */
    private Integer signCount;
    /**
     * 自定义参数值
     */
    private Map<String, String> extMap;

    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     * 观众id
     */
    private List<Long> ids;

    /**
     * 时间筛选类型 10 本年度 20 去年  30 本月
     */
    private Integer dateScreeType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最近扫码签到成员名称
     */
    private Integer lastSignMemberName;
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 关键字搜索
     */
    private String keyword;
}