/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoExhibitorEmailRecords;
import lombok.Data;

import java.util.List;

/**
 * ExpoExhibitorEmailRecords  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
public class ExpoExhibitorEmailRecordsDTO extends ExpoExhibitorEmailRecords {

    /**
     * ids
     */
    private List<Integer> ids;
    /**
     * 邮件主题
     */
    private String subject;
    /**
     * 邮件模板
     */
    private Integer templateId;
    /**
     * 邮件配置
     */
    private Integer configId;

}