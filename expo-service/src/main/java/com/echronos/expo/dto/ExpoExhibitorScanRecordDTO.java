/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoExhibitorScanRecord;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ExpoExhibitorScanRecord  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
public class ExpoExhibitorScanRecordDTO extends ExpoExhibitorScanRecord {

    /**
     * 展商名称
     */
    private String exhibitorName;
    /**
     * 观众名称
     */
    private String audienceName;
    /**
     * 被扫观众信息
     */
    private List<ExpoExhibitorScanAudienceInfoDTO> audienceInfo;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}