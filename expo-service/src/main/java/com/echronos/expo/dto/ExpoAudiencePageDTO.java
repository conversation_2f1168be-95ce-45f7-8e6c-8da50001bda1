/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.iform.api.entity.CollectFieldSort;
import com.echronos.iform.api.entity.CollectFilter;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * ExpoAudience  Dto 对象
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class ExpoAudiencePageDTO extends ExpoAudienceDTO {

    private Integer pageNo;

    private Integer pageSize;

    private String keywords;
    /**
     * 高级筛选返回的条件SQL
     */
    private String whereSqlStr = "";

    /**
     * 高级筛选返回的排序SQL
     */
    private String sortSqlStr = "";

    /**
     * 高级筛选排序条件
     */
    private String sortStr;

    /**
     * 查询列名
     */
    private List<String> fieldList;

    /**
     * 观众ID集合
     */
    private List<Integer> idList;
    /**
     * 产品或服务
     */
    private List<String> productsAndServiceList;
    /**
     * 行业
     */
    private List<String> industryCodeList;
    /**
     * 成为买家
     */
    private List<String> buyerList;
    /**
     * 职位
     */
    private List<String> positionList;
    /**
     * 年采购
     */
    private List<String> procurementList;
    /**
     * 年销售收入
     */
    private List<String> revenueList;
    /**
     * 商贸配对计划
     */
    private List<String> ifAttendBusinessList;

    /**
     * 会议
     */
    private List<String> ifAttendConferenceList;

    /**
     * 筛选条件
     */
    private List<CollectFilter> filters;
    /**
     * 排序条件
     */
    private List<CollectFieldSort> sort;
}