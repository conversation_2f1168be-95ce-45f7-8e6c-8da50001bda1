package com.echronos.expo.dto;

import com.echronos.expo.model.ExpoAppointedPersonnel;
import lombok.Data;

import java.util.List;

/**
 * ExpoAppointedPersonnel  Dto 对象
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ExpoAppointedPersonnelDTO extends ExpoAppointedPersonnel {
    /**
     * 成员id
     */
    private List<Integer> memberIds;
    /**
     * 是否开启预约：0-否 1-是
     */
    private Integer isAppoint;
}