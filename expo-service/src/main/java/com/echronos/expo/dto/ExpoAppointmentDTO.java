/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoAppointment;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * ExpoAppointment  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ExpoAppointmentDTO extends ExpoAppointment {
    /**
     * 附件
     */
    private List<ExpoAttachmentFileDTO> attachmentList;
    /**
     * 时间段
     */
    private List<ExpoAppointmentTimeDTO> timeList;
    /**
     * 时间段ID
     */
    private Set<Integer> timeIds;
    /**
     * 搜索关键词
     */
    private String keywords;
}