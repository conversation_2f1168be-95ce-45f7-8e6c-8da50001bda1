package com.echronos.expo.api.feign;

import com.echronos.commons.Result;
import com.echronos.expo.api.req.ExpoIdReq;
import com.echronos.expo.api.resp.ExpoInfoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025-08-18 20:06
 */
@FeignClient(value = "ech-expo")
public interface IExpoInfoFeign {

    /**
     * 根据ID获取展位信息
     *
     * @param req
     * @return
     */
    @GetMapping(value = "api/get/info/by/id")
    Result<ExpoInfoResp> getExpoInfoById(@RequestBody ExpoIdReq req);

}
